{"model_config": {"model_path": "Meta-Llama-3.1-8B-Instruct", "base_url": "http://localhost:8883", "api_key": null}, "dataset_config": {"dataset": "GSM8K", "num_samples": 200, "few_shot": 8}, "search_config": {"n_trials": 30, "study_name": "Llama3.1_8B_instruct_search", "storage": "sqlite:///optuna_study.db"}, "fixed_params": {"max_tokens": 512, "top_p": 0.9}, "search_space": {"n_consistency_samples": {"type": "int", "low": 20, "high": 40}}, "description": {"model_path": "vLLM服务器上的模型路径", "base_url": "vLLM服务器地址", "dataset": "评估数据集名称（MMLU/GSM8K等）", "num_samples": "每次试验使用的样本数（建议100-500）", "few_shot": "Few-shot示例数量", "n_trials": "Optuna试验次数", "n_consistency_samples": "Self-consistency采样次数搜索范围"}}