{"humaneval": {"description": "HumanEval代码生成基准测试，评估模型在Python函数完成任务上的表现", "dataset_config": {"dataset_name": "openai/humaneval", "split": "test", "num_samples": 164, "task_type": "code_generation"}, "evaluation_params": {"temperature": 0.2, "top_p": 0.95, "max_tokens": 1024, "few_shot": 0, "n_consistency_samples": 10, "use_cot": true, "use_path_confidence": true, "combined_confidence_threshold": 0.3, "temperature_confidence": 0.1}, "prompt_template": {"instruction": "请完成以下Python函数。仔细分析函数要求，并生成完整的实现。", "cot_template": "让我逐步分析这个问题：\n1. 理解函数要求\n2. 分析输入输出\n3. 设计算法\n4. 实现代码\n\n", "output_format": "```python\n{code}\n```"}, "metrics": ["pass@1", "pass@10", "pass@100", "code_bleu", "functional_correctness"]}, "humaneval-x": {"description": "HumanEval-X多语言代码生成基准测试，支持Python、Java、JavaScript、C++、Go", "dataset_config": {"dataset_name": "THUDM/humaneval-x", "split": "test", "num_samples": 164, "task_type": "multilingual_code_generation", "languages": ["python", "java", "javascript", "cpp", "go"]}, "evaluation_params": {"temperature": 0.2, "top_p": 0.95, "max_tokens": 1024, "few_shot": 0, "n_consistency_samples": 10, "use_cot": true, "use_path_confidence": true, "combined_confidence_threshold": 0.3, "temperature_confidence": 0.1}, "prompt_template": {"instruction": "请完成以下{language}函数。仔细分析函数要求，并生成完整的实现。", "cot_template": "让我逐步分析这个{language}编程问题：\n1. 理解函数要求\n2. 分析输入输出\n3. 设计算法\n4. 实现代码\n\n", "output_format": "```{language}\n{code}\n```"}, "metrics": ["pass@1", "pass@10", "functional_correctness", "syntax_correctness"]}, "mbpp": {"description": "MBPP (Mostly Basic Python Problems) 基础Python编程问题基准测试", "dataset_config": {"dataset_name": "mbpp", "split": "test", "num_samples": 500, "task_type": "python_code_generation"}, "evaluation_params": {"temperature": 0.2, "top_p": 0.95, "max_tokens": 512, "few_shot": 3, "n_consistency_samples": 5, "use_cot": true, "use_path_confidence": true, "combined_confidence_threshold": 0.25, "temperature_confidence": 0.1}, "prompt_template": {"instruction": "请根据问题描述编写Python函数。", "cot_template": "让我分析这个Python编程问题：\n1. 理解问题要求\n2. 分析测试用例\n3. 设计解决方案\n4. 编写代码\n\n", "output_format": "```python\n{code}\n```"}, "metrics": ["pass@1", "pass@10", "functional_correctness"]}, "codegen": {"description": "通用代码生成评估配置", "dataset_config": {"task_type": "code_generation"}, "evaluation_params": {"temperature": 0.2, "top_p": 0.95, "max_tokens": 1024, "few_shot": 0, "n_consistency_samples": 10, "use_cot": true, "use_path_confidence": true, "combined_confidence_threshold": 0.3, "temperature_confidence": 0.1}, "prompt_template": {"instruction": "请根据要求生成代码。", "cot_template": "让我逐步分析并解决这个编程问题：\n", "output_format": "```{language}\n{code}\n```"}, "execution_evaluation": {"enabled": true, "timeout": 10, "max_memory_mb": 512, "safety_checks": true}}}