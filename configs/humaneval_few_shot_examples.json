{"humaneval_few_shot_examples": [{"problem": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n", "solution_with_cot": "让我逐步分析这个问题：\n1. 需要检查列表中任意两个数字是否距离小于阈值\n2. 可以用双重循环比较所有数字对\n3. 如果找到任何一对距离小于阈值的数字，返回True\n4. 如果所有数字对都不满足条件，返回False\n\n```python\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if abs(numbers[i] - numbers[j]) < threshold:\n                return True\n    return False\n```", "solution_direct": "```python\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if abs(numbers[i] - numbers[j]) < threshold:\n                return True\n    return False\n```"}, {"problem": "def separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    separate those group and return the list of those.\n    Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    Ignore any spaces in the input string.\n    >>> separate_paren_groups('( ) (( )) (( )( ))')\n    ['()', '(())', '(()())']\n    \"\"\"\n", "solution_with_cot": "让我逐步分析这个问题：\n1. 需要将字符串中的多个括号组分离\n2. 每个组都是平衡的括号\n3. 需要忽略空格\n4. 可以用计数器跟踪括号平衡状态\n\n```python\ndef separate_paren_groups(paren_string: str) -> List[str]:\n    result = []\n    current_group = \"\"\n    balance = 0\n    \n    for char in paren_string:\n        if char == ' ':\n            continue\n        \n        current_group += char\n        \n        if char == '(':\n            balance += 1\n        elif char == ')':\n            balance -= 1\n        \n        if balance == 0:\n            result.append(current_group)\n            current_group = \"\"\n    \n    return result\n```", "solution_direct": "```python\ndef separate_paren_groups(paren_string: str) -> List[str]:\n    result = []\n    current_group = \"\"\n    balance = 0\n    \n    for char in paren_string:\n        if char == ' ':\n            continue\n        \n        current_group += char\n        \n        if char == '(':\n            balance += 1\n        elif char == ')':\n            balance -= 1\n        \n        if balance == 0:\n            result.append(current_group)\n            current_group = \"\"\n    \n    return result\n```"}, {"problem": "def truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    and integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \"\"\"\n", "solution_with_cot": "让我逐步分析这个问题：\n1. 需要获取浮点数的小数部分\n2. 小数部分 = 原数 - 整数部分\n3. 可以使用模运算符%来获取小数部分\n4. 或者使用int()函数获取整数部分后相减\n\n```python\ndef truncate_number(number: float) -> float:\n    return number % 1\n```", "solution_direct": "```python\ndef truncate_number(number: float) -> float:\n    return number % 1\n```"}], "mbpp_few_shot_examples": [{"problem": "Write a function to find the similar elements from the given two tuple lists.", "solution_with_cot": "让我分析这个Python编程问题：\n1. 需要找到两个元组列表中的相似元素\n2. 可以使用集合的交集操作\n3. 将元组列表转换为集合后求交集\n\n```python\ndef similar_elements(test_tup1, test_tup2):\n    return tuple(set(test_tup1) & set(test_tup2))\n```", "solution_direct": "```python\ndef similar_elements(test_tup1, test_tup2):\n    return tuple(set(test_tup1) & set(test_tup2))\n```"}, {"problem": "Write a python function to identify non-prime numbers.", "solution_with_cot": "让我分析这个Python编程问题：\n1. 需要识别非质数\n2. 质数是只能被1和自身整除的大于1的数\n3. 非质数包括1和所有合数\n4. 可以通过检查是否有除1和自身外的因子来判断\n\n```python\ndef is_not_prime(n):\n    if n < 2:\n        return True\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return True\n    return False\n```", "solution_direct": "```python\ndef is_not_prime(n):\n    if n < 2:\n        return True\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return True\n    return False\n```"}], "prompt_templates": {"humaneval_cot": {"system": "你是一个专业的Python程序员。请仔细分析问题，使用逐步推理的方法完成代码。", "user": "请完成以下Python函数。\n\n{problem}\n\n请按照以下步骤：\n1. 理解函数要求和预期行为\n2. 分析输入输出的关系\n3. 设计算法或解决方案\n4. 实现完整的代码\n\n请提供你的分析过程和最终代码。", "response_format": "分析：\n{analysis}\n\n代码：\n```python\n{code}\n```"}, "humaneval_direct": {"system": "你是一个专业的Python程序员。请直接完成给定的函数。", "user": "请完成以下Python函数：\n\n{problem}", "response_format": "```python\n{code}\n```"}, "mbpp_cot": {"system": "你是一个专业的Python程序员。请仔细分析问题要求，编写正确的Python代码。", "user": "问题：{problem}\n\n请按照以下步骤解决：\n1. 理解问题要求\n2. 分析可能的测试用例\n3. 设计解决方案\n4. 编写Python代码\n\n请提供你的分析和代码。", "response_format": "分析：\n{analysis}\n\n代码：\n```python\n{code}\n```"}}}