{"file_info": {"file_path": "test_data_with_hesitation.json", "total_questions": 3, "total_paths": 12}, "hesitation_analysis": {"questions_with_hesitation": 0, "paths_with_hesitation": 0, "total_hesitation_words": 0, "word_frequency": {}, "hesitation_vs_confidence": [{"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}, {"hesitation_score": 0, "confidence": 0.0, "has_hesitation": false}], "hesitation_vs_correctness": [{"question_index": 0, "is_correct": true, "question_hesitation_score": 0, "has_hesitation": false, "confidence": 0.9}, {"question_index": 1, "is_correct": false, "question_hesitation_score": 0, "has_hesitation": false, "confidence": 0.6}, {"question_index": 2, "is_correct": true, "question_hesitation_score": 0, "has_hesitation": false, "confidence": 0.8}], "detailed_stats": {"correct_with_hesitation": 0, "correct_without_hesitation": 2, "incorrect_with_hesitation": 0, "incorrect_without_hesitation": 1}}, "question_details": [{"question_index": 0, "question": "What is 2 + 2?", "gold_answer": "4", "final_answer": "4", "is_correct": true, "confidence": 0.9, "paths": [{"path_index": 0, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 1, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 2, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 3, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}], "question_hesitation_score": 0}, {"question_index": 1, "question": "What is 5 * 3?", "gold_answer": "15", "final_answer": "12", "is_correct": false, "confidence": 0.6, "paths": [{"path_index": 0, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 1, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 2, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 3, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}], "question_hesitation_score": 0}, {"question_index": 2, "question": "What is 10 - 3?", "gold_answer": "7", "final_answer": "7", "is_correct": true, "confidence": 0.8, "paths": [{"path_index": 0, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 1, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 2, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}, {"path_index": 3, "reasoning": "", "answer": "", "path_confidence": 0.0, "hesitation_words": {}, "hesitation_score": 0, "has_hesitation": false}], "question_hesitation_score": 0}]}