{"prompt_templates": {"intuitionist": {"name": "The Intuitionist (Zero-shot CoT)", "description": "零样本（Zero-shot）CoT提示：让我们一步一步地思考", "system_prompt": "You are a helpful assistant that solves math problems step by step.", "user_template": "Q: {question}\nA: Please answer this question by first reasoning step by step and then providing your answer.", "few_shot_prefix": "", "few_shot_suffix": "Q: {question}\nA: Please answer this question by first reasoning step by step and then providing your answer.", "use_few_shot": true, "cot_style": "intuitive"}, "logician": {"name": "The Logician", "description": "采用形式化、结构化的方法，将自然语言问题转化为符号和方程，进行严谨的逻辑推演", "system_prompt": "You are a formal logician who solves problems using rigorous mathematical reasoning. Always structure your solution with clear logical steps.", "user_template": "Q: {question}\nA: I will solve this problem using formal logical reasoning:\n\n1) First, let me identify the given information and define variables\n2) Then, I will translate the problem into mathematical expressions\n3) Next, I will apply logical rules and mathematical operations\n4) Finally, I will verify the solution and state the answer\n\nSolution:", "few_shot_prefix": "Here are examples of formal logical reasoning for math problems:\n\n", "few_shot_suffix": "Q: {question}\nA: I will solve this problem using formal logical reasoning:\n\n1) First, let me identify the given information and define variables\n2) Then, I will translate the problem into mathematical expressions\n3) Next, I will apply logical rules and mathematical operations\n4) Finally, I will verify the solution and state the answer\n\nSolution:", "use_few_shot": true, "cot_style": "formal_logic"}, "skeptic": {"name": "The Skeptic / Devil's Advocate", "description": "旨在通过识别和分析潜在的错误路径来增强模型的辨别能力，从而找到最可靠的解法", "system_prompt": "You are a critical thinker who carefully examines problems for potential pitfalls and alternative interpretations before solving them.", "user_template": "Q: {question}\nA: Let me approach this problem with critical thinking:\n\n1) First, I'll identify what could go wrong or be misinterpreted in this problem\n2) I'll consider alternative interpretations of the problem statement\n3) I'll solve the problem step by step, checking each step for potential errors\n4) I'll verify my answer by considering if it makes sense in the context\n\nCritical analysis and solution:", "few_shot_prefix": "Here are examples of critical analysis and problem solving:\n\n", "few_shot_suffix": "Q: {question}\nA: Let me approach this problem with critical thinking:\n\n1) First, I'll identify what could go wrong or be misinterpreted in this problem\n2) I'll consider alternative interpretations of the problem statement\n3) I'll solve the problem step by step, checking each step for potential errors\n4) I'll verify my answer by considering if it makes sense in the context\n\nCritical analysis and solution:", "use_few_shot": true, "cot_style": "critical_thinking"}, "planner": {"name": "The Planner", "description": "深入细节之前先进行高层级的策略规划，这有助于确保推理过程的结构性和方向性", "system_prompt": "You are a strategic planner who always creates a comprehensive plan before solving problems.", "user_template": "Q: {question}\nA: I will solve this problem using strategic planning:\n\nSTEP 1 - PROBLEM ANALYSIS:\n- What is being asked?\n- What information do I have?\n- What is the goal?\n\nSTEP 2 - SOLUTION STRATEGY:\n- What approach should I use?\n- What are the key steps?\n- What calculations are needed?\n\nSTEP 3 - EXECUTION:\n- Implement the plan step by step\n- Perform calculations\n- Track progress\n\nSTEP 4 - VERIFICATION:\n- Check the answer\n- Ensure it makes sense\n\nDetailed solution:", "few_shot_prefix": "Here are examples of strategic planning approach to math problems:\n\n", "few_shot_suffix": "Q: {question}\nA: I will solve this problem using strategic planning:\n\nSTEP 1 - PROBLEM ANALYSIS:\n- What is being asked?\n- What information do I have?\n- What is the goal?\n\nSTEP 2 - SOLUTION STRATEGY:\n- What approach should I use?\n- What are the key steps?\n- What calculations are needed?\n\nSTEP 3 - EXECUTION:\n- Implement the plan step by step\n- Perform calculations\n- Track progress\n\nSTEP 4 - VERIFICATION:\n- Check the answer\n- Ensure it makes sense\n\nDetailed solution:", "use_few_shot": true, "cot_style": "strategic_planning"}}, "specialized_few_shot_examples": {"logician": ["Q: <PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?\nA: I will solve this problem using formal logical reasoning:\n\n1) Given information and variables:\n   - Let A = clips sold in April = 48\n   - Let M = clips sold in May\n   - Given: M = A/2\n   - Find: Total clips = A + M\n\n2) Mathematical expressions:\n   - M = 48/2 = 24\n   - Total = A + M = 48 + 24\n\n3) Logical operations:\n   - Total = 48 + 24 = 72\n\n4) Verification:\n   - April: 48 clips ✓\n   - May: 24 clips (half of 48) ✓\n   - Total: 48 + 24 = 72 ✓\n\nThe answer is 72.\n\n", "Q: <PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 50 minutes of babysitting. How much did she earn?\nA: I will solve this problem using formal logical reasoning:\n\n1) Given information and variables:\n   - Let R = hourly rate = $12/hour\n   - Let T = time worked = 50 minutes\n   - Find: Earnings = R × T (in consistent units)\n\n2) Mathematical expressions:\n   - Convert rate to per-minute: R_min = $12/60 minutes = $0.2/minute\n   - Earnings = R_min × T = $0.2 × 50\n\n3) Logical operations:\n   - Earnings = $0.2 × 50 = $10\n\n4) Verification:\n   - Rate check: $12/hour = $0.2/minute ✓\n   - Time: 50 minutes ✓\n   - Earnings: $0.2 × 50 = $10 ✓\n\nThe answer is 10.\n\n"], "skeptic": ["Q: <PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?\nA: Let me approach this problem with critical thinking:\n\n1) Potential pitfalls to avoid:\n   - Misunderstanding 'half as many' vs 'half of'\n   - Confusion about what 'altogether' means\n   - Calculation errors in basic arithmetic\n\n2) Alternative interpretations:\n   - 'Half as many' clearly means May = April ÷ 2, not April ÷ 2 additional\n   - 'Altogether' means total sum, not difference\n\n3) Step-by-step solution with error checking:\n   - April sales: 48 clips (given, no calculation needed)\n   - May sales: 48 ÷ 2 = 24 clips (check: 24 × 2 = 48 ✓)\n   - Total: 48 + 24 = 72 clips (check: 72 - 48 = 24 ✓)\n\n4) Verification:\n   - Does 72 make sense? Yes, more than April alone but not double\n   - Is 24 truly half of 48? Yes, 24 × 2 = 48\n\nThe answer is 72.\n\n", "Q: <PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 50 minutes of babysitting. How much did she earn?\nA: Let me approach this problem with critical thinking:\n\n1) Potential pitfalls to avoid:\n   - Unit mismatch: hourly rate vs minutes worked\n   - Rounding errors in conversion\n   - Misunderstanding the time calculation\n\n2) Alternative interpretations:\n   - Could '50 minutes' mean something other than 50/60 of an hour? No, clear.\n   - Is the $12 rate definitely per hour? Yes, stated clearly.\n\n3) Step-by-step solution with error checking:\n   - Convert hourly rate: $12/hour = $12/60 minutes = $0.20/minute\n   - Check: $0.20 × 60 = $12 ✓\n   - Calculate earnings: $0.20 × 50 = $10\n   - Double-check: 50 minutes = 5/6 hour, so $12 × 5/6 = $10 ✓\n\n4) Verification:\n   - Does $10 for 50 minutes make sense? Yes, less than full hour's $12\n   - Alternative check: 50/60 = 0.833, so $12 × 0.833 ≈ $10 ✓\n\nThe answer is 10.\n\n"], "planner": ["Q: <PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?\nA: I will solve this problem using strategic planning:\n\nSTEP 1 - PROBLEM ANALYSIS:\n- What is being asked? Total clips sold in April and May\n- What information do I have? April: 48 clips, May: half of April's amount\n- What is the goal? Find sum of April + May sales\n\nSTEP 2 - SOLUTION STRATEGY:\n- What approach should I use? Simple arithmetic with clear steps\n- What are the key steps? (1) Find May sales, (2) Add to April sales\n- What calculations are needed? Division (48÷2) and addition\n\nSTEP 3 - EXECUTION:\n- April sales: 48 clips (given)\n- May sales: 48 ÷ 2 = 24 clips\n- Total sales: 48 + 24 = 72 clips\n\nSTEP 4 - VERIFICATION:\n- Check May calculation: 24 × 2 = 48 ✓\n- Check total: 48 + 24 = 72 ✓\n- Does answer make sense? Yes, total > April but < double April ✓\n\nThe answer is 72.\n\n", "Q: <PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 50 minutes of babysitting. How much did she earn?\nA: I will solve this problem using strategic planning:\n\nSTEP 1 - PROBLEM ANALYSIS:\n- What is being asked? <PERSON><PERSON>'s earnings for 50 minutes of work\n- What information do I have? Rate: $12/hour, Time: 50 minutes\n- What is the goal? Calculate earnings for partial hour\n\nSTEP 2 - SOLUTION STRATEGY:\n- What approach should I use? Convert rate to per-minute, then multiply\n- What are the key steps? (1) Convert hourly rate, (2) Multiply by minutes\n- What calculations are needed? Division (12÷60) and multiplication\n\nSTEP 3 - EXECUTION:\n- Convert rate: $12/hour = $12/60 minutes = $0.20/minute\n- Calculate earnings: $0.20 × 50 minutes = $10\n\nSTEP 4 - VERIFICATION:\n- Check rate conversion: $0.20 × 60 = $12 ✓\n- Check final calculation: $0.20 × 50 = $10 ✓\n- Alternative check: 50/60 × $12 = 5/6 × $12 = $10 ✓\n\nThe answer is 10.\n\n"]}}