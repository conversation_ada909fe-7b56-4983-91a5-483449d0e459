#!/usr/bin/env python3
"""
测试犹豫词汇检测功能
"""

import sys
sys.path.append('/home/<USER>/Documentation/ICLR26/CISC')

from analyze_hesitation_words import extract_hesitation_words

# 测试文本
test_texts = [
    "There are 50 deer, and 50 percent of them are bucks, so there are 50 * 0.5 = 25 bucks. However, 20 percent of the bucks are 8 points.",
    "First, we find out how many bucks there are. Actually, let me think about this more carefully.",
    "Wait, I think I made a mistake. Let me recalculate this problem.",
    "The answer should be 5, but I'm not sure if that's correct.",
    "50 percent of the deer are bucks, so 50 / 2 = 25 are bucks."
]

print("🧪 测试犹豫词汇检测功能")

for i, text in enumerate(test_texts, 1):
    print(f"\n📝 测试文本 {i}: {text[:60]}...")
    hesitation = extract_hesitation_words(text)
    if hesitation:
        print(f"   🚨 发现犹豫词汇: {hesitation}")
    else:
        print(f"   ✅ 未发现犹豫词汇")

print("\n🔍 测试单个词汇:")
single_tests = ["however", "actually", "wait", "let me think", "I think"]
for word in single_tests:
    result = extract_hesitation_words(f"This is a test with {word} in it.")
    print(f"  '{word}': {'✅' if result else '❌'} {result}")
