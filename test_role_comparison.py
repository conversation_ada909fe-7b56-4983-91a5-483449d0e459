#!/usr/bin/env python3
"""
测试多角色对比评估系统
验证四种prompt模板是否能正常协作工作
"""

import sys
from pathlib import Path
import json

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

def test_template_loading():
    """测试模板加载"""
    print("🧪 测试GSM8K多角色模板加载...")
    
    try:
        # 直接导入，避免相对导入问题
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from local_datasets import LocalGSM8KProcessor
        
        processor = LocalGSM8KProcessor()
        processor.load_prompt_templates()
        
        roles = ["intuitionist", "logician", "skeptic", "planner"]
        
        for role in roles:
            try:
                formatted_prompt = processor.format_prompt(
                    question="简单测试：2+3等于多少？",
                    template_name=role,
                    few_shot=1
                )
                print(f"✅ {role} 模板加载成功 (长度: {len(formatted_prompt)})")
            except Exception as e:
                print(f"❌ {role} 模板加载失败: {e}")
                return False
        
        print("🎉 所有角色模板加载成功！")
        return True
        
    except Exception as e:
        print(f"❌ 模板系统加载失败: {e}")
        print("💡 提示：可能需要先配置Python环境或检查src/local_datasets.py文件")
        return False

def test_comparison_script():
    """测试对比脚本结构"""
    print(f"\n🔧 测试对比脚本结构...")
    
    script_path = Path("scripts/role_run/run_gsm8k_compare_roles.py")
    
    if not script_path.exists():
        print(f"❌ 对比脚本不存在: {script_path}")
        return False
    
    # 检查脚本是否可以导入主要类
    try:
        sys.path.append(str(script_path.parent))
        
        # 简单的语法检查
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查关键组件
        required_components = [
            "RoleComparisonEvaluator",
            "create_visualization", 
            "main",
            "intuitionist",
            "logician",
            "skeptic", 
            "planner"
        ]
        
        for component in required_components:
            if component not in script_content:
                print(f"❌ 缺少关键组件: {component}")
                return False
        
        print("✅ 对比脚本结构检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 对比脚本检查失败: {e}")
        return False

def test_shell_script():
    """测试shell脚本"""
    print(f"\n🐚 测试Shell脚本...")
    
    shell_script = Path("scripts/role_run/run_gsm8k_all_role.sh")
    
    if not shell_script.exists():
        print(f"❌ Shell脚本不存在: {shell_script}")
        return False
    
    try:
        with open(shell_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查关键参数
        required_params = [
            "--model",
            "--base_url", 
            "--num_samples",
            "--few_shot",
            "run_gsm8k_compare_roles.py"
        ]
        
        for param in required_params:
            if param not in script_content:
                print(f"❌ Shell脚本缺少参数: {param}")
                return False
        
        print("✅ Shell脚本检查通过")
        return True
        
    except Exception as e:
        print(f"❌ Shell脚本检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 GSM8K多角色对比评估系统测试")
    print("=" * 60)
    
    tests = [
        ("模板加载测试", test_template_loading),
        ("对比脚本测试", test_comparison_script),
        ("Shell脚本测试", test_shell_script)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！多角色对比评估系统已准备就绪。")
        print(f"\n📋 使用方法:")
        print(f"   # 快速测试（20道题）")
        print(f"   ./scripts/role_run/run_gsm8k_all_role.sh --quick")
        print(f"   ")
        print(f"   # 完整测试（100道题）")
        print(f"   ./scripts/role_run/run_gsm8k_all_role.sh --full")
        print(f"   ")
        print(f"   # 自定义测试")
        print(f"   ./scripts/role_run/run_gsm8k_all_role.sh --num_samples 30 --port 3333")
        return True
    else:
        print(f"\n❌ 部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
