#!/usr/bin/env python3
"""
使用Optuna进行超参数搜索
搜索最优的n_consistency_samples和temperature参数
"""

import os
os.environ["CUDA_VISIBLE_DEVICES"] = '4'
import sys
import json
import logging
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.local_datasets import get_local_dataset_processor, sample_local_dataset
from src.vllm_models import VLLMModelWrapper, VLLMConfig, test_vllm_connection
from src.vllm_evaluator import VLLMSelfConsistencyEvaluator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_file: str = "configs/optuna_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"配置文件 {config_file} 不存在，使用默认配置")
        return get_default_config()

def get_default_config() -> Dict[str, Any]:
    """获取默认配置"""
    return {
        "model_config": {
            "model_path": "/data/",
            # "model_path": "/data/Mistral-7B-Instruct-v0.3",
            "base_url": "http://localhost:8895",
            # "base_url": "http://localhost:8888",
            "api_key": None
        },
        "dataset_config": {
            "dataset": "MMLU",
            "num_samples": 1000,
            "few_shot": 5
        },
        "search_config": {
            "n_trials": 30,
            "study_name": "minstral_7b_search",
            "storage": "sqlite:///optuna_mixtral_study.db"
        },
        "fixed_params": {
            "max_tokens": 1,
            "top_p": 0.9
        },
        "search_space": {
            "n_consistency_samples": {
                "type": "int",
                "low": 1,
                "high": 10
            },
            "temperature": {
                "type": "float",
                "low": 0.1,
                "high": 1.0,
                "step": 0.1
            }
        }
    }

class OptunaHyperparameterSearch:
    """Optuna超参数搜索类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.vllm_wrapper = None
        
    def create_vllm_wrapper(self):
        """创建vLLM模型包装器"""
        model_config = self.config["model_config"]
        config = VLLMConfig(
            base_url=model_config["base_url"],
            model_name=model_config["model_path"],
            api_key=model_config.get("api_key")
        )
        return VLLMModelWrapper(config)
    
    def objective(self, trial):
        """Optuna目标函数"""
        # 根据配置定义搜索空间
        params = {}
        for param_name, param_config in self.config["search_space"].items():
            if param_config["type"] == "int":
                params[param_name] = trial.suggest_int(
                    param_name, 
                    param_config["low"], 
                    param_config["high"]
                )
            elif param_config["type"] == "float":
                if "step" in param_config:
                    params[param_name] = trial.suggest_float(
                        param_name,
                        param_config["low"],
                        param_config["high"],
                        step=param_config["step"]
                    )
                else:
                    params[param_name] = trial.suggest_float(
                        param_name,
                        param_config["low"],
                        param_config["high"]
                    )
        
        logger.info(f"🔍 Trial {trial.number}: {params}")
        
        try:
            # 创建vLLM包装器
            if self.vllm_wrapper is None:
                self.vllm_wrapper = self.create_vllm_wrapper()
            
            # 创建评估器
            evaluator_params = {
                "vllm_wrapper": self.vllm_wrapper,
                "few_shot": self.config["dataset_config"]["few_shot"]
            }
            
            # 添加固定参数
            evaluator_params.update(self.config["fixed_params"])
            
            # 添加搜索参数，注意参数名映射
            for param_name, param_value in params.items():
                if param_name == "n_consistency_samples":
                    # 评估器使用的是n_samples参数
                    evaluator_params["n_samples"] = param_value
                else:
                    evaluator_params[param_name] = param_value
            
            evaluator = VLLMSelfConsistencyEvaluator(**evaluator_params)
            
            # 加载数据集
            processor = get_local_dataset_processor(self.config["dataset_config"]["dataset"])
            test_data = sample_local_dataset(processor, self.config["dataset_config"]["num_samples"])
            
            # 运行评估
            start_time = time.time()
            results = evaluator.evaluate_dataset(processor, test_data)
            evaluation_time = time.time() - start_time
            
            # 记录结果
            accuracy = results.accuracy
            logger.info(f"✅ Trial {trial.number} 完成: 准确率={accuracy:.4f}, 评估时间={evaluation_time:.2f}s")
            
            # 记录额外信息到trial
            trial.set_user_attr('accuracy', accuracy)
            trial.set_user_attr('evaluation_time', evaluation_time)
            trial.set_user_attr('total_samples', results.total_samples)
            trial.set_user_attr('avg_inference_time', results.avg_inference_time)
            
            # 返回准确率作为优化目标（Optuna默认最大化）
            return accuracy
            
        except Exception as e:
            logger.error(f"❌ Trial {trial.number} 失败: {e}")
            # 返回一个很低的值，表示这个试验失败
            return 0.0
    
    def run_search(self):
        """运行超参数搜索"""
        logger.info("🚀 开始Optuna超参数搜索")
        logger.info(f"📝 搜索配置: {self.config}")
        
        # 测试vLLM连接
        logger.info("🔗 测试vLLM服务器连接...")
        if not test_vllm_connection(self.config["model_config"]["base_url"]):
            logger.error(f"❌ 无法连接到vLLM服务器: {self.config['model_config']['base_url']}")
            return None
        
        # 导入optuna（延迟导入，避免未安装时的错误）
        try:
            import optuna
        except ImportError:
            logger.error("❌ 未安装Optuna，请运行: pip install optuna")
            return None
        
        # 创建study
        search_config = self.config["search_config"]
        study = optuna.create_study(
            study_name=search_config["study_name"],
            storage=search_config["storage"],
            direction='maximize',  # 最大化准确率
            load_if_exists=True
        )
        
        logger.info(f"📊 开始 {search_config['n_trials']} 次试验...")
        
        # 运行优化
        study.optimize(self.objective, n_trials=search_config["n_trials"])
        
        # 输出结果
        logger.info("🎉 超参数搜索完成！")
        logger.info(f"🏆 最佳参数:")
        for param, value in study.best_params.items():
            logger.info(f"   - {param}: {value}")
        logger.info(f"   - 最佳准确率: {study.best_value:.4f}")
        
        # 保存结果
        self.save_results(study)
        
        return study
    
    def save_results(self, study):
        """保存搜索结果"""
        results_dir = Path("optuna_results")
        results_dir.mkdir(exist_ok=True)
        
        # 保存最佳参数
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        best_params_file = results_dir / f"best_params_{timestamp}.json"
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "config": self.config,
            "best_params": study.best_params,
            "best_value": study.best_value,
            "n_trials": len(study.trials)
        }
        
        with open(best_params_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 最佳参数已保存到: {best_params_file}")
        
        # 保存所有试验结果
        trials_file = results_dir / f"all_trials_{timestamp}.json"
        trials_data = []
        
        for trial in study.trials:
            trial_data = {
                "number": trial.number,
                "params": trial.params,
                "value": trial.value,
                "state": trial.state.name,
                "user_attrs": trial.user_attrs
            }
            trials_data.append(trial_data)
        
        with open(trials_file, 'w', encoding='utf-8') as f:
            json.dump(trials_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 所有试验结果已保存到: {trials_file}")
        
        # 生成报告
        self.generate_report(study, results_dir / f"search_report_{timestamp}.txt")
    
    def generate_report(self, study, output_file: Path):
        """生成搜索报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Optuna超参数搜索报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"搜索时间: {datetime.now().isoformat()}\n")
            f.write(f"搜索配置: {self.config}\n\n")
            
            f.write("最佳结果:\n")
            f.write(f"  准确率: {study.best_value:.4f}\n")
            f.write(f"  参数: {study.best_params}\n\n")
            
            f.write("参数重要性:\n")
            try:
                import optuna
                importance = optuna.importance.get_param_importances(study)
                for param, imp in importance.items():
                    f.write(f"  {param}: {imp:.4f}\n")
            except Exception as e:
                f.write(f"  无法计算参数重要性: {e}\n")
            f.write("\n")
            
            f.write("前10个最佳试验:\n")
            f.write("-" * 50 + "\n")
            sorted_trials = sorted(study.trials, key=lambda t: t.value if t.value else 0, reverse=True)
            
            for i, trial in enumerate(sorted_trials[:10]):
                if trial.value is not None:
                    f.write(f"#{i+1}: 准确率={trial.value:.4f}, 参数={trial.params}\n")
            
            f.write(f"\n总试验次数: {len(study.trials)}\n")
            f.write(f"成功试验次数: {len([t for t in study.trials if t.value is not None])}\n")
        
        logger.info(f"📋 搜索报告已保存到: {output_file}")

def analyze_results(study_name: Optional[str] = None, storage: str = "sqlite:///optuna_study.db"):
    """分析已有的搜索结果"""
    try:
        import optuna
    except ImportError:
        logger.error("❌ 未安装Optuna，请运行: pip install optuna")
        return None
    
    if study_name is None:
        study_name = "vllm_hyperparameter_search"
    
    try:
        study = optuna.load_study(
            study_name=study_name,
            storage=storage
        )
        
        logger.info(f"📊 分析study: {study_name}")
        logger.info(f"试验总数: {len(study.trials)}")
        logger.info(f"最佳准确率: {study.best_value:.4f}")
        logger.info(f"最佳参数: {study.best_params}")
        
        # 生成可视化（如果安装了可视化库）
        try:
            import optuna.visualization as vis
            
            results_dir = Path("optuna_results")
            results_dir.mkdir(exist_ok=True)
            
            # 参数重要性图
            fig = vis.plot_param_importances(study)
            fig.write_html(str(results_dir / "param_importances.html"))
            
            # 优化历史图
            fig = vis.plot_optimization_history(study)
            fig.write_html(str(results_dir / "optimization_history.html"))
            
            logger.info("📈 可视化图表已保存到 optuna_results/ 目录")
            
        except ImportError:
            logger.info("⚠️ 未安装可视化库，跳过图表生成")
        
        return study
        
    except Exception as e:
        logger.error(f"❌ 无法加载study: {e}")
        return None

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optuna超参数搜索")
    parser.add_argument("--mode", choices=["search", "analyze"], default="search",
                       help="运行模式：search（搜索）或analyze（分析）")
    parser.add_argument("--config", default="configs/optuna_config_mixtral.json",
                       help="配置文件路径")
    parser.add_argument("--n_trials", type=int, default=50,
                       help="搜索试验次数（覆盖配置文件）")
    parser.add_argument("--num_samples", type=int, default=5000,
                       help="每次试验使用的样本数（覆盖配置文件）")
    
    args = parser.parse_args()
    
    try:
        if args.mode == "search":
            # 加载配置
            config = load_config(args.config)
            
            # 命令行参数覆盖配置文件
            # if args.n_trials:
            #     config["search_config"]["n_trials"] = args.n_trials
            # if args.num_samples:
            #     config["dataset_config"]["num_samples"] = args.num_samples
            
            # 运行搜索
            searcher = OptunaHyperparameterSearch(config)
            study = searcher.run_search()
            
            if study:
                logger.info("🎉 超参数搜索完成！")
            
        elif args.mode == "analyze":
            config = load_config(args.config)
            study = analyze_results(
                config["search_config"]["study_name"],
                config["search_config"]["storage"]
            )
            if study:
                logger.info("📊 结果分析完成！")
                
    except Exception as e:
        logger.error(f"💥 运行过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
