2025-09-02 22:16:56,268 - INFO -    - 置信度温度 (T_conf): 1.0
2025-09-02 22:16:56,268 - INFO - 🚀 开始使用vLLM API运行Self-Consistency Baseline评估
2025-09-02 22:16:56,268 - INFO - 📝 评估配置:
2025-09-02 22:16:56,268 - INFO -    - 模型: ['Mistral-7B-Instruct-v0.3']
2025-09-02 22:16:56,268 - INFO -    - 数据集: ['gsm8k']
2025-09-02 22:16:56,268 - INFO -    - CoT模式: 启用
2025-09-02 22:16:56,268 - INFO -    - Prompt模板: logician
2025-09-02 22:16:56,268 - INFO -    - Few-shot示例数: 8
2025-09-02 22:16:56,268 - INFO -    - Self-consistency采样次数: 40
2025-09-02 22:16:56,268 - INFO -    - Temperature: 0.7
2025-09-02 22:16:56,268 - INFO -    - Top-p: 0.9
2025-09-02 22:16:56,268 - INFO -    - 最大tokens: 512
2025-09-02 22:16:56,268 - INFO -    - 路径置信度评分: 启用
2025-09-02 22:16:56,268 - INFO -    - 组合置信度阈值: 0.0
2025-09-02 22:16:56,268 - INFO -    - 实时保存: 启用（每10个样本保存进度）
2025-09-02 22:16:56,268 - INFO -    - 保留进度文件: 否
2025-09-02 22:16:56,268 - INFO - 🔗 测试vLLM服务器连接...
2025-09-02 22:16:56,272 - INFO - 
🤖 开始评估模型: Mistral-7B-Instruct-v0.3
2025-09-02 22:16:56,275 - INFO - ✅ 成功连接到vLLM服务器: http://localhost:2222
2025-09-02 22:16:56,275 - INFO - 📋 可用模型: ['Mistral-7B-Instruct-v0.3']
2025-09-02 22:16:56,275 - INFO - 
📊 评估数据集: gsm8k
2025-09-02 22:16:56,280 - INFO - 从本地加载GSM8K测试集: 1319 个样本
2025-09-02 22:16:56,303 - INFO - 从本地加载GSM8K训练集: 7473 个样本
2025-09-02 22:16:56,304 - INFO - 从本地加载GSM8K CoT示例: 10 个
2025-09-02 22:16:56,304 - INFO - 加载GSM8K prompt模板: ['intuitionist', 'logician', 'skeptic', 'planner']
2025-09-02 22:16:56,304 - INFO - 📈 使用 5 个样本进行评估
2025-09-02 22:16:56,304 - INFO - 开始使用vLLM评估数据集: GSM8K
2025-09-02 22:16:56,304 - INFO - 🎯 使用Prompt模板: logician - The Logician
2025-09-02 22:16:56,304 - INFO - 📝 模板描述: 采用形式化、结构化的方法，将自然语言问题转化为符号和方程，进行严谨的逻辑推演
2025-09-02 22:16:56,304 - INFO - 评估样本数: 5
2025-09-02 22:16:56,304 - INFO - 评估进度: 1/5
2025-09-02 22:17:07,843 - INFO - 评估进度: 2/5
2025-09-02 22:17:19,805 - INFO - 评估进度: 3/5
2025-09-02 22:17:32,214 - INFO - 评估进度: 4/5
2025-09-02 22:17:43,520 - INFO - 评估进度: 5/5
2025-09-02 22:17:55,034 - INFO - 当前准确率: 0.6000, 平均tokens: 17352.00
2025-09-02 22:17:55,036 - WARNING - 保存进度时出错: free variable 'safe_model_name' referenced before assignment in enclosing scope
2025-09-02 22:17:55,042 - INFO - 评估结果已保存到: results/vllm_Mistral-7B-Instruct-v0.3_gsm8k_20250902_221755.json
2025-09-02 22:17:55,042 - INFO - 📊 开始保存路径详细信息到JSON: results/paths_detail_Mistral-7B-Instruct-v0.3_gsm8k_20250902_221755.json
2025-09-02 22:17:55,050 - INFO - ✅ 路径详细信息已保存到JSON: results/paths_detail_Mistral-7B-Instruct-v0.3_gsm8k_20250902_221755.json
2025-09-02 22:17:55,050 - INFO - 📈 统计信息: 5 个问题, 200 条路径
2025-09-02 22:17:55,050 - INFO - 📊 路径详细信息已保存到JSON: results/paths_detail_Mistral-7B-Instruct-v0.3_gsm8k_20250902_221755.json
2025-09-02 22:17:55,051 - INFO - 💾 部分结果已保存到: results/partial_results_Mistral-7B-Instruct-v0.3_20250902_221755.json
2025-09-02 22:17:55,051 - INFO - ✅ 结果摘要 - gsm8k:
2025-09-02 22:17:55,051 - INFO -    🎯 准确率: 0.6000 (60.00%)
2025-09-02 22:17:55,051 - INFO -    📝 平均tokens/问题: 17352.00
2025-09-02 22:17:55,051 - INFO -    🔢 总tokens: 86,760
2025-09-02 22:17:55,051 - INFO -    ⏱️ 平均推理时间: 11.75s
2025-09-02 22:17:55,051 - INFO -    🚀 吞吐量: 1477.3 tokens/s
2025-09-02 22:17:55,051 - INFO - 
💾 完整结果已保存到: results/vllm_baseline_results_20250902_221755.json
2025-09-02 22:17:55,052 - INFO - 📋 摘要报告已保存到: results/vllm_baseline_summary_20250902_221755.txt
2025-09-02 22:17:55,053 - INFO - 🎉 vLLM Baseline评估完成！
✅ vLLM服务器连接成功: http://localhost:2222
📋 可用模型: ['Mistral-7B-Instruct-v0.3']
