2025-09-03 01:23:15,971 - INFO -    - 置信度温度 (T_conf): 0.2
2025-09-03 01:23:15,971 - INFO - 🚀 开始使用vLLM API运行Self-Consistency Baseline评估
2025-09-03 01:23:15,971 - INFO - 📝 评估配置:
2025-09-03 01:23:15,971 - INFO -    - 模型: ['Mistral-7B-Instruct-v0.3']
2025-09-03 01:23:15,971 - INFO -    - 数据集: ['gsm8k']
2025-09-03 01:23:15,971 - INFO -    - CoT模式: 启用
2025-09-03 01:23:15,971 - INFO -    - Prompt模板: logician
2025-09-03 01:23:15,971 - INFO -    - Few-shot示例数: 8
2025-09-03 01:23:15,971 - INFO -    - Self-consistency采样次数: 40
2025-09-03 01:23:15,971 - INFO -    - Temperature: 0.7
2025-09-03 01:23:15,971 - INFO -    - Top-p: 0.9
2025-09-03 01:23:15,971 - INFO -    - 最大tokens: 512
2025-09-03 01:23:15,971 - INFO -    - 路径置信度评分: 启用
2025-09-03 01:23:15,971 - INFO -    - 组合置信度阈值: 0.25
2025-09-03 01:23:15,971 - INFO - 🔗 测试vLLM服务器连接...
2025-09-03 01:23:15,975 - INFO - 
🤖 开始评估模型: Mistral-7B-Instruct-v0.3
2025-09-03 01:23:15,977 - INFO - ✅ 成功连接到vLLM服务器: http://localhost:2222
2025-09-03 01:23:15,977 - INFO - 📋 可用模型: ['Mistral-7B-Instruct-v0.3']
2025-09-03 01:23:15,978 - INFO - 
📊 评估数据集: gsm8k
2025-09-03 01:23:15,982 - INFO - 从本地加载GSM8K测试集: 1319 个样本
2025-09-03 01:23:16,003 - INFO - 从本地加载GSM8K训练集: 7473 个样本
2025-09-03 01:23:16,003 - INFO - 从本地加载GSM8K CoT示例: 10 个
2025-09-03 01:23:16,003 - INFO - 加载GSM8K prompt模板: ['intuitionist', 'logician', 'skeptic', 'planner']
2025-09-03 01:23:16,003 - INFO - 📈 使用 1319 个样本进行评估
2025-09-03 01:23:16,003 - INFO - 开始使用vLLM评估数据集: GSM8K
2025-09-03 01:23:16,003 - INFO - 🎯 使用Prompt模板: logician - The Logician
2025-09-03 01:23:16,003 - INFO - 📝 模板描述: 采用形式化、结构化的方法，将自然语言问题转化为符号和方程，进行严谨的逻辑推演
2025-09-03 01:23:16,004 - INFO - 评估样本数: 1319
2025-09-03 01:23:16,004 - INFO - 评估进度: 1/1319
2025-09-03 01:23:27,488 - INFO - 问题序号: 0，是否做对: True
2025-09-03 01:23:27,488 - INFO - 评估进度: 2/1319
2025-09-03 01:23:36,895 - INFO - 问题序号: 1，是否做对: True
2025-09-03 01:23:36,895 - INFO - 评估进度: 3/1319
