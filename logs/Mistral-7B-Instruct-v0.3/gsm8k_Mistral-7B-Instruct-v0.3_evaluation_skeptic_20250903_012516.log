2025-09-03 01:25:18,422 - INFO -    - 置信度温度 (T_conf): 0.2
2025-09-03 01:25:18,423 - INFO - 🚀 开始使用vLLM API运行Self-Consistency Baseline评估
2025-09-03 01:25:18,423 - INFO - 📝 评估配置:
2025-09-03 01:25:18,423 - INFO -    - 模型: ['Mistral-7B-Instruct-v0.3']
2025-09-03 01:25:18,423 - INFO -    - 数据集: ['gsm8k']
2025-09-03 01:25:18,423 - INFO -    - CoT模式: 启用
2025-09-03 01:25:18,423 - INFO -    - Prompt模板: skeptic
2025-09-03 01:25:18,423 - INFO -    - Few-shot示例数: 8
2025-09-03 01:25:18,423 - INFO -    - Self-consistency采样次数: 40
2025-09-03 01:25:18,423 - INFO -    - Temperature: 0.7
2025-09-03 01:25:18,423 - INFO -    - Top-p: 0.9
2025-09-03 01:25:18,423 - INFO -    - 最大tokens: 512
2025-09-03 01:25:18,423 - INFO -    - 路径置信度评分: 启用
2025-09-03 01:25:18,423 - INFO -    - 组合置信度阈值: 0.25
2025-09-03 01:25:18,423 - INFO - 🔗 测试vLLM服务器连接...
2025-09-03 01:25:18,428 - INFO - 
🤖 开始评估模型: Mistral-7B-Instruct-v0.3
2025-09-03 01:25:18,430 - INFO - ✅ 成功连接到vLLM服务器: http://localhost:2222
2025-09-03 01:25:18,430 - INFO - 📋 可用模型: ['Mistral-7B-Instruct-v0.3']
2025-09-03 01:25:18,430 - INFO - 
📊 评估数据集: gsm8k
2025-09-03 01:25:18,435 - INFO - 从本地加载GSM8K测试集: 1319 个样本
2025-09-03 01:25:18,455 - INFO - 从本地加载GSM8K训练集: 7473 个样本
2025-09-03 01:25:18,455 - INFO - 从本地加载GSM8K CoT示例: 10 个
2025-09-03 01:25:18,456 - INFO - 加载GSM8K prompt模板: ['intuitionist', 'logician', 'skeptic', 'planner']
2025-09-03 01:25:18,456 - INFO - 📈 使用 1319 个样本进行评估
2025-09-03 01:25:18,456 - INFO - 开始使用vLLM评估数据集: GSM8K
2025-09-03 01:25:18,456 - INFO - 🎯 使用Prompt模板: skeptic - The Skeptic / Devil's Advocate
2025-09-03 01:25:18,456 - INFO - 📝 模板描述: 旨在通过识别和分析潜在的错误路径来增强模型的辨别能力，从而找到最可靠的解法
2025-09-03 01:25:18,456 - INFO - 评估样本数: 1319
2025-09-03 01:25:18,456 - INFO - 评估进度: 1/1319
2025-09-03 01:25:30,186 - INFO - 问题序号: 0，是否做对: False
2025-09-03 01:25:30,186 - INFO - 回答: 60
2025-09-03 01:25:30,186 - INFO - 正确答案: Janet sells 16 - 3 - 4 = <<16-3-4=9>>9 duck eggs a day.
She makes 9 * 2 = $<<9*2=18>>18 every day at the farmer’s market.
#### 18
2025-09-03 01:25:30,186 - INFO - 评估进度: 2/1319
2025-09-03 01:25:41,345 - INFO - 问题序号: 1，是否做对: True
2025-09-03 01:25:41,345 - INFO - 评估进度: 3/1319
2025-09-03 01:25:53,027 - INFO - 问题序号: 2，是否做对: False
2025-09-03 01:25:53,027 - INFO - 回答: 000
2025-09-03 01:25:53,027 - INFO - 正确答案: The cost of the house and repairs came out to 80,000+50,000=$<<80000+50000=130000>>130,000
He increased the value of the house by 80,000*1.5=<<80000*1.5=120000>>120,000
So the new value of the house is 120,000+80,000=$<<120000+80000=200000>>200,000
So he made a profit of 200,000-130,000=$<<200000-130000=70000>>70,000
#### 70000
2025-09-03 01:25:53,027 - INFO - 评估进度: 4/1319
2025-09-03 01:26:04,161 - INFO - 问题序号: 3，是否做对: True
2025-09-03 01:26:04,161 - INFO - 评估进度: 5/1319
