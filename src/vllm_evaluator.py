"""
使用vLLM API的Self-Consistency评估器
"""

import json
import time
import torch
import re
import math
import random
from collections import defaultdict
from collections import Counter
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import logging

from .vllm_models import VLLMModelWrapper, VLLMSelfConsistencyWrapper
from .local_datasets import LocalDatasetProcessor

logger = logging.getLogger(__name__)

@dataclass
class VLLMEvaluationResult:
    """单个样本的vLLM评估结果"""
    question_id: int
    question: str
    gold_answer: str
    responses: List[str]
    extracted_answers: List[str]
    #confidence_scores: List[float] # NEW: 置信度分数列表
    answer_confidence_scores: List[float] # Renamed for clarity
    path_confidence_scores: List[float]   # NEW: Path confidence scores
    combined_confidence_scores: List[float] # NEW: Combined scores
    final_answer: str
    is_correct: bool
    total_tokens: int
    prompt_tokens: int
    completion_tokens: int
    inference_time: float
    gpt_validations: Optional[List[Dict[str, Any]]] = None  # NEW: GPT验证结果

@dataclass
class VLLMModelResults:
    """vLLM模型在数据集上的整体结果"""
    model_name: str
    dataset_name: str
    accuracy: float
    total_samples: int
    correct_samples: int
    avg_tokens_per_sample: float
    total_tokens: int
    avg_prompt_tokens: float
    avg_completion_tokens: float
    avg_inference_time: float
    total_inference_time: float
    sample_results: List[VLLMEvaluationResult]

class VLLMSelfConsistencyEvaluator:
    """使用vLLM API的Self-Consistency评估器"""
    
    def __init__(self, vllm_wrapper: VLLMModelWrapper, n_samples: int = 5, 
                 temperature: float = 0.7, top_p: float = 0.9, max_tokens: int = 512,
                 few_shot: int = 0, confidence_temp: float = 1.0, use_cot: bool = False, use_path_confidence: bool = False, 
                 combined_confidence_threshold: float = 0.0, prompt_template: str = "intuitionist"):
        self.vllm_wrapper = vllm_wrapper
        self.sc_wrapper = VLLMSelfConsistencyWrapper(vllm_wrapper)
        self.n_samples = n_samples
        self.temperature = temperature
        self.top_p = top_p
        self.max_tokens = max_tokens
        self.few_shot = few_shot
        ## NEW ##
        self.confidence_temp = confidence_temp 
        ## NEW CoT implementation
        self.use_cot = use_cot # 新增 use_cot
        ### NEW confidence threshold
        self.use_path_confidence = use_path_confidence
        self.combined_confidence_threshold = combined_confidence_threshold
        self.prompt_template = prompt_template  # 添加prompt模板参数
        
        # GPT验证相关属性
        self.gpt_validator = None
        self.gpt_validation_sample_rate = 0.0
        self.gpt_validation_results = []
        
        # 获取 "True" 和 "False" 的 token ID。这假设你的模型tokenizer将它们识别为单个token。
        # 注意: 这是一个简化。理想情况下，你应该从vLLM服务器的tokenizer获取。
        # vLLM API不直接暴露tokenizer，但我们可以通过logprobs的输出来验证。
        # 我们将直接使用字符串"True"和"False"。
        # self.true_token_str = "True"
        # self.false_token_str = "False"
        self.true_tokens = [" T", "T", " t", "T.", " t.", "t"]
        self.false_tokens = [" F", "F", " f", "F.", " f.", "f"]
    
    def set_gpt_validator(self, gpt_validator, sample_rate: float = 0.2):
        """设置GPT验证器 - 兼容性方法"""
        self.gpt_validator = gpt_validator
        self.gpt_validation_sample_rate = sample_rate
        logger.info(f"GPT验证器已设置，采样率: {sample_rate}")
    
    def _should_validate_with_gpt(self) -> bool:
        """根据采样率决定是否使用GPT验证 - 兼容性方法"""
        return (self.gpt_validator is not None and 
                random.random() < self.gpt_validation_sample_rate)


    def _calculate_confidence(self, logprobs: Dict[str, float]) -> float:
        """
        从第一个生成token的logprobs计算置信度分数 P(T)。
        这个函数健壮地实现了带温度的Softmax逻辑。
        """
        # 如果vLLM没有返回logprobs，我们无法判断，返回一个中性值。
        if not logprobs:
            logger.warning("没有返回logprobs，无法计算置信度。返回中性值0.5")
            return 0.5

        # 步骤1: 对所有候选token的logits应用温度缩放 (logit / T)
        # T > 1.0 会使概率更平滑，降低高低置信度的差距。
        # T < 1.0 会使概率更尖锐，放大高低置信度的差距。
        scaled_logprobs = {
            token: logit / self.confidence_temp for token, logit in logprobs.items()
        }

        # 步骤2: 计算“正面”和“负面”信号的未归一化概率总和
        # 我们计算 exp(scaled_logit)，这对应于Softmax的分子部分。
        # 我们将一个“家族”（如所有代表“是”的token）的概率加起来。
        prob_true = sum(
            math.exp(scaled_logprobs.get(t, -1e9)) for t in self.true_tokens
        )

        prob_false = sum(
            math.exp(scaled_logprobs.get(f, -1e9)) for f in self.false_tokens
        )
        
        # 步骤3: 在“正面”和“负面”集合之间进行归一化
        # 这相当于一个局部的、二元的Softmax，正是我们想要的。
        # Confidence = P(True) / (P(True) + P(False))
        total_prob = prob_true + prob_false
        
        if total_prob > 1e-9:  # 避免除以零
            confidence = prob_true / total_prob
        else:
            # 如果在候选token中完全没有找到任何T/F的变体，则无法判断。
            confidence = 0.5  # 返回中性值

        return confidence
    

    ## NEW HELPER ##
    def _extract_reasoning_and_answer(self, response: str) -> Tuple[str, str]:
        """从CoT响应中分离推理路径和最终答案"""
        match = re.search(r'The final answer is', response, re.IGNORECASE)
        if match:
            # 分割点是 "The final answer is" 的开始位置
            split_point = match.start()
            reasoning_path = response[:split_point].strip()
            # 答案部分是分割点之后的所有内容
            answer_part = response[split_point:].strip()
            return reasoning_path, answer_part
        else:
            # 如果找不到标准格式，整个响应被视为推理，答案部分为空
            return response.strip(), ""

    
    def evaluate_sample(self, example: Dict[str, Any], processor: LocalDatasetProcessor, 
                       question_id: int, exclude_indices: Optional[set] = None) -> VLLMEvaluationResult:
        """评估单个样本,支持路径和答案双重置信度"""
        start_time = time.time()
        
        # 获取few-shot示例
        few_shot_examples = None
        if self.few_shot > 0:
            # 排除当前问题的索引
            exclude_set = exclude_indices or set()
            exclude_set.add(question_id)
            ###CoT implementation
            # 检查processor是否支持prompt_template参数
            import inspect
            sig = inspect.signature(processor.get_few_shot_examples)
            if 'prompt_template' in sig.parameters:
                few_shot_examples = processor.get_few_shot_examples(self.few_shot, exclude_set, use_cot=self.use_cot, prompt_template=self.prompt_template)
            else:
                few_shot_examples = processor.get_few_shot_examples(self.few_shot, exclude_set, use_cot=self.use_cot)

        # 格式化prompt（包含few-shot示例）
        ###CoT implementation
        if hasattr(processor, 'format_prompt'):
            # 检查processor是否支持prompt_template参数
            import inspect
            sig = inspect.signature(processor.format_prompt)
            if 'prompt_template' in sig.parameters:
                prompt = processor.format_prompt(example, few_shot_examples, use_cot=self.use_cot, prompt_template=self.prompt_template)
            else:
                prompt = processor.format_prompt(example, few_shot_examples, use_cot=self.use_cot)
        else:
            # 回退到基本格式
            prompt = f"Q: {example.get('question', str(example))}\nA:"

        # 获取金标准答案(correct option not used in finding)
        if isinstance(example, dict):
            if "answer" in example:
                gold_answer = example["answer"]
            elif "correct_answer" in example:
                gold_answer = example["correct_answer"]
            else:
                # 对于MMLU/MMLU-Pro，答案可能在其他字段
                gold_answer = example.get("answer_idx", example.get("correct_option", ""))
        else:
            gold_answer = str(example.answer if hasattr(example, 'answer') else "")
        
        try:
            # 生成多个响应
            generation_results = self.sc_wrapper.generate_multiple(
                prompt=prompt,
                n_samples=self.n_samples,
                temperature=self.temperature,
                top_p=self.top_p,
                max_tokens=self.max_tokens
            )

            # 提取响应文本
            responses = [res["generated_text"] for res in generation_results]
            
            # 初始化统计
            extracted_answers = []
            answer_confidences = []
            path_confidences = []
            combined_confidences = []

            total_tokens = sum(res.get("total_tokens", 0) for res in generation_results)
            total_prompt_tokens = sum(res.get("prompt_tokens", 0) for res in generation_results)
            total_completion_tokens = sum(res.get("completion_tokens", 0) for res in generation_results)
            
            # 步骤2: 为每个响应计算置信度
            for response_text in responses:
                # 提取答案
                ###CoT implementation

                # 1. 提取答案和推理
                reasoning_path, answer_part = self._extract_reasoning_and_answer(response_text)
                answer = processor.extract_answer(response_text, use_cot=self.use_cot)
                extracted_answers.append(answer)
                
                # 2. 计算答案置信度
                answer_confidence = 0.5 # 默认值

                try:
                    # 使用整个响应来判断答案，因为上下文可能很重要
                    answer_conf_prompt = processor.format_confidence_prompt(example, response_text)
                    _, logprobs = self.vllm_wrapper.generate_with_logprobs(answer_conf_prompt)

                    answer_confidence = self._calculate_confidence(logprobs)

                except Exception as e:
                    logger.warning(f"为样本 {question_id} 计算答案置信度失败: {e}。使用默认值0.5")
                answer_confidences.append(answer_confidence)

                # 3. 计算路径置信度 (如果启用)
                path_confidence = 1.0 # 如果不启用，路径置信度为1.0，不影响结果
                if self.use_path_confidence and reasoning_path:
                    try:
                        path_conf_prompt = processor.format_path_confidence_prompt(example, reasoning_path)
                        _, logprobs = self.vllm_wrapper.generate_with_logprobs(path_conf_prompt)

                        path_confidence = self._calculate_confidence(logprobs)
                    except Exception as e:
                        logger.warning(f"为样本 {question_id} 计算路径置信度失败: {e}。使用默认值0.5")
                        path_confidence = 0.5
                elif self.use_path_confidence and not reasoning_path:
                    logger.warning(f"样本 {question_id} 无法提取推理路径，路径置信度设为0")
                    path_confidence = 0.0
                path_confidences.append(path_confidence)

                # 4. 计算组合置信度
                combined_confidences.append(answer_confidence * path_confidence)

            # 5. GPT验证（如果启用且满足采样条件）
            gpt_validations = []
            if self._should_validate_with_gpt():
                logger.info(f"对样本 {question_id} 进行GPT验证")
                for i, (response_text, answer_conf, path_conf) in enumerate(zip(responses, answer_confidences, path_confidences)):
                    reasoning_path, answer_part = self._extract_reasoning_and_answer(response_text)
                    extracted_answer = extracted_answers[i]
                    
                    # 验证答案置信度
                    answer_validation = self.gpt_validator.validate_answer_confidence(
                        prompt, extracted_answer, answer_conf
                    )
                    
                    # 验证路径置信度（如果启用且有推理路径）
                    path_validation = {"status": "skipped"}
                    if self.use_path_confidence and reasoning_path:
                        path_validation = self.gpt_validator.validate_path_confidence(
                            prompt, reasoning_path, path_conf
                        )
                    
                    gpt_validations.append({
                        "response_index": i,
                        "answer_validation": answer_validation,
                        "path_validation": path_validation
                    })
                
                # 保存验证结果用于后续分析
                self.gpt_validation_results.append({
                    "question_id": question_id,
                    "validations": gpt_validations
                })

            # 6. 根据组合置信度阈值过滤并投票
            final_answer = self._filter_and_vote(
                extracted_answers, 
                combined_confidences, 
                self.combined_confidence_threshold
            )
            
            is_correct = processor.check_answer(final_answer, str(gold_answer))
            inference_time = time.time() - start_time

            return VLLMEvaluationResult(
                question_id=question_id, question=prompt, gold_answer=str(gold_answer),
                responses=responses, extracted_answers=extracted_answers,
                answer_confidence_scores=answer_confidences,
                path_confidence_scores=path_confidences,
                combined_confidence_scores=combined_confidences,
                final_answer=final_answer, is_correct=is_correct,
                total_tokens=total_tokens, prompt_tokens=total_prompt_tokens,
                completion_tokens=total_completion_tokens, inference_time=inference_time,
                gpt_validations=gpt_validations if gpt_validations else None
            )

                
        except Exception as e:
            logger.error(f"评估样本 {question_id} 时出错: {e}", exc_info=True)
            inference_time = time.time() - start_time
                
            return VLLMEvaluationResult(
                question_id=question_id, question=prompt, gold_answer=str(gold_answer),
                responses=[f"ERROR: {str(e)}"], extracted_answers=[""],
                answer_confidence_scores=[], path_confidence_scores=[], combined_confidence_scores=[],
                final_answer="", is_correct=False, total_tokens=0, prompt_tokens=0,
                completion_tokens=0, inference_time=inference_time
            )





    ## NEW ##
    def _filter_and_vote(self, answers: List[str], confidences: List[float], threshold: float) -> str:
        """根据置信度阈值过滤，然后进行加权投票"""
        if not answers:
            return ""

        # 过滤掉无效答案
        valid_pairs = [(ans, conf) for ans, conf in zip(answers, confidences) if ans.strip()]
        if not valid_pairs:
            return ""

        # 根据阈值进行过滤
        high_confidence_pairs = [pair for pair in valid_pairs if pair[1] >= threshold]

        # 如果过滤后没有剩下任何答案，退回到在所有有效答案上进行加权投票
        if not high_confidence_pairs:
            logger.warning(f"所有回答的组合置信度都低于阈值 {threshold}。将使用所有原始回答进行投票。")
            target_pairs = valid_pairs
        else:
            target_pairs = high_confidence_pairs

        # 在目标对上进行加权投票
        weighted_votes = defaultdict(float)
        for answer, confidence in target_pairs:
            weighted_votes[answer] += confidence
        
        if not weighted_votes:
            # 理论上不会发生，因为我们已经处理了空列表的情况，但作为安全措施
            return valid_pairs[0][0]

        # 返回权重最高的答案
        return max(weighted_votes, key=weighted_votes.get)

    

    ## RENAMED & MODIFIED ##
    def _weighted_vote(self, answers: List[str], confidences: List[float]) -> str:
        """使用加权投票决定最终答案"""
        if not answers:
            return ""

        valid_answers = [ans for ans in answers if ans.strip()]
        if not valid_answers:
            return ""

        # 如果置信度列表长度不匹配，退回到多数投票
        if len(answers) != len(confidences):
            logger.warning("答案和置信度数量不匹配，退回到多数投票")
            vote_counter = Counter(valid_answers)
            return vote_counter.most_common(1)[0][0]

        # 计算加权投票
        weighted_votes = defaultdict(float)
        for answer, confidence in zip(answers, confidences):
            if answer.strip():
                weighted_votes[answer] += confidence
        
        if not weighted_votes:
            # 如果所有答案都无效或置信度为0，退回到多数投票
            vote_counter = Counter(valid_answers)
            return vote_counter.most_common(1)[0][0]
        
        
        # 返回权重最高的答案
        return max(weighted_votes, key=weighted_votes.get)
    

    def _majority_vote(self, answers: List[str]) -> str:
        """多数投票决定最终答案"""
        if not answers:
            return ""
        
        # 过滤空答案
        valid_answers = [ans for ans in answers if ans.strip()]
        
        if not valid_answers:
            return ""
        
        # 统计投票
        vote_counter = Counter(valid_answers)
        
        # 返回得票最多的答案
        most_common = vote_counter.most_common(1)
        if most_common:
            return most_common[0][0]
        
        return valid_answers[0]  # 如果没有明确的多数，返回第一个
    
    def evaluate_dataset(self, processor: LocalDatasetProcessor, 
                        test_data: Optional[List[Dict[str, Any]]] = None,
                        num_samples: Optional[int] = None,
                        save_progress_callback: Optional[callable] = None) -> VLLMModelResults:
        """评估整个数据集，支持增量保存"""
        logger.info(f"开始使用vLLM评估数据集: {processor.dataset_name}")
        
        # 记录prompt模板信息（仅对GSM8K）
        if hasattr(processor, 'get_available_templates') and self.prompt_template:
            available_templates = processor.get_available_templates()
            if self.prompt_template in available_templates:
                template_name = available_templates[self.prompt_template]
                template_desc = processor.get_template_description(self.prompt_template)
                logger.info(f"🎯 使用Prompt模板: {self.prompt_template} - {template_name}")
                logger.info(f"📝 模板描述: {template_desc}")
            else:
                logger.warning(f"⚠️ 指定的模板 '{self.prompt_template}' 不存在，使用默认模板")
        
        # 确定要评估的数据
        if test_data is not None:
            samples = test_data
        elif processor.dataset is not None:
            if num_samples is None:
                samples = processor.dataset
            else:
                samples = processor.dataset[:num_samples]
        else:
            raise ValueError("No test data available")
        
        logger.info(f"评估样本数: {len(samples)}")
        
        sample_results = []
        correct_count = 0
        total_tokens = 0
        total_prompt_tokens = 0
        total_completion_tokens = 0
        total_time = 0
        
        # 逐个评估样本
        used_indices = set()  # 跟踪已使用的few-shot示例索引
        for i, example in enumerate(samples):
            logger.info(f"评估进度: {i+1}/{len(samples)}")
            
            result = self.evaluate_sample(example, processor, i, used_indices)
            logger.info(f"问题序号: {result.question_id}，是否做对: {result.is_correct}")
            # 如果没做对，打印更多信息
            if not result.is_correct:
                # logger.info(f"问题: {result.question}")
                logger.info(f"回答: {result.final_answer}")
                logger.info(f"正确答案: {result.gold_answer}")
                # logger.info(f"回答置信度: {result.answer_confidence_scores}")
                # logger.info(f"路径置信度: {result.path_confidence_scores}")
                # logger.info(f"组合置信度: {result.combined_confidence_scores}")
            sample_results.append(result)
            
            if result.is_correct:
                correct_count += 1
            
            total_tokens += result.total_tokens
            total_prompt_tokens += result.prompt_tokens
            total_completion_tokens += result.completion_tokens
            total_time += result.inference_time
            
            # 每10个样本报告一次进度，并调用保存回调
            if (i + 1) % 10 == 0 or (i + 1) == len(samples):
                current_acc = correct_count / (i + 1)
                avg_tokens = total_tokens / (i + 1)
                logger.info(f"当前准确率: {current_acc:.4f}, 平均tokens: {avg_tokens:.2f}")
                
                # 如果有保存回调函数，调用它来保存进度
                if save_progress_callback:
                    # 创建当前的中间结果
                    model_info = self.vllm_wrapper.get_model_info()
                    model_name = model_info.get("id", "unknown")
                    
                    intermediate_result = VLLMModelResults(
                        model_name=model_name,
                        dataset_name=processor.dataset_name,
                        accuracy=current_acc,
                        total_samples=i + 1,
                        correct_samples=correct_count,
                        avg_tokens_per_sample=avg_tokens,
                        total_tokens=total_tokens,
                        avg_prompt_tokens=total_prompt_tokens / (i + 1),
                        avg_completion_tokens=total_completion_tokens / (i + 1),
                        avg_inference_time=total_time / (i + 1),
                        total_inference_time=total_time,
                        sample_results=sample_results.copy()  # 传递当前已完成的样本
                    )
                    
                    try:
                        save_progress_callback(intermediate_result, i + 1, len(samples))
                    except Exception as e:
                        logger.warning(f"保存进度时出错: {e}")
        
        # 计算最终统计
        accuracy = correct_count / len(samples) if samples else 0.0
        avg_tokens_per_sample = total_tokens / len(samples) if samples else 0.0
        avg_prompt_tokens = total_prompt_tokens / len(samples) if samples else 0.0
        avg_completion_tokens = total_completion_tokens / len(samples) if samples else 0.0
        avg_inference_time = total_time / len(samples) if samples else 0.0
        
        model_info = self.vllm_wrapper.get_model_info()
        model_name = model_info.get("id", "unknown")
        
        return VLLMModelResults(
            model_name=model_name,
            dataset_name=processor.dataset_name,
            accuracy=accuracy,
            total_samples=len(samples),
            correct_samples=correct_count,
            avg_tokens_per_sample=avg_tokens_per_sample,
            total_tokens=total_tokens,
            avg_prompt_tokens=avg_prompt_tokens,
            avg_completion_tokens=avg_completion_tokens,
            avg_inference_time=avg_inference_time,
            total_inference_time=total_time,
            sample_results=sample_results
        )
    
    def get_evaluation_summary(self, results: VLLMModelResults) -> Dict[str, Any]:
        """获取评估摘要"""
        return {
            "model_name": results.model_name,
            "dataset_name": results.dataset_name,
            "accuracy": results.accuracy,
            "accuracy_percentage": results.accuracy * 100,
            "total_samples": results.total_samples,
            "correct_samples": results.correct_samples,
            "wrong_samples": results.total_samples - results.correct_samples,
            "avg_tokens_per_sample": results.avg_tokens_per_sample,
            "total_tokens": results.total_tokens,
            "avg_prompt_tokens": results.avg_prompt_tokens, 
            "avg_completion_tokens": results.avg_completion_tokens,
            "avg_inference_time": results.avg_inference_time,
            "total_inference_time": results.total_inference_time,
            "tokens_per_second": results.total_tokens / results.total_inference_time if results.total_inference_time > 0 else 0,
            "questions_per_second": results.total_samples / results.total_inference_time if results.total_inference_time > 0 else 0
        }

def save_vllm_results(results: VLLMModelResults, output_file: str):
    """保存vLLM评估结果到文件"""
    # 创建一个临时评估器来获取摘要（不需要实际的wrapper）
    evaluator_summary = {
        "model_name": results.model_name,
        "dataset_name": results.dataset_name,
        "accuracy": results.accuracy,
        "accuracy_percentage": results.accuracy * 100,
        "total_samples": results.total_samples,
        "correct_samples": results.correct_samples,
        "wrong_samples": results.total_samples - results.correct_samples,
        "avg_tokens_per_sample": results.avg_tokens_per_sample,
        "total_tokens": results.total_tokens,
        "avg_prompt_tokens": results.avg_prompt_tokens, 
        "avg_completion_tokens": results.avg_completion_tokens,
        "avg_inference_time": results.avg_inference_time,
        "total_inference_time": results.total_inference_time,
        "tokens_per_second": results.total_tokens / results.total_inference_time if results.total_inference_time > 0 else 0,
        "questions_per_second": results.total_samples / results.total_inference_time if results.total_inference_time > 0 else 0
    }
    
    output_data = {
        "model_results": asdict(results),
        "evaluation_summary": evaluator_summary
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"评估结果已保存到: {output_file}")
