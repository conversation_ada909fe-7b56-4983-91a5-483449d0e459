#!/usr/bin/env python3
"""
GSM8K多角色对比评估脚本
对每道题依次使用四种prompt模板（直觉主义者、逻辑学家、怀疑论者、规划者）
记录每种角色在相同题目上的对错分布，并生成可视化对比图表
"""

import json
import argparse
import time
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import pandas as pd
from collections import Counter

# 直接导入同目录下的模块
import sys
from pathlib import Path

# 添加当前目录到Python路径，以支持绝对导入
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from vllm_models import VLLMModelWrapper, VLLMConfig, VLLMSelfConsistencyWrapper
from local_datasets import get_local_dataset_processor, sample_local_dataset

# 日志配置
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RoleComparisonEvaluator:
    """多角色对比评估器"""
    
    def __init__(self, vllm_wrapper, evaluation_config):
        self.vllm_wrapper = vllm_wrapper
        self.config = evaluation_config
        self.roles = ["intuitionist", "logician", "skeptic", "planner"]
        self.role_names = {
            "intuitionist": "直觉主义者",
            "logician": "逻辑学家", 
            "skeptic": "怀疑论者",
            "planner": "规划者"
        }
        
    def evaluate_single_question(self, processor, question_data: dict, question_idx: int) -> Dict:
        """对单个问题使用所有角色进行评估"""
        
        results = {
            "question_id": question_idx,
            "question": question_data["question"],
            "ground_truth": question_data["answer"],
            "roles": {}
        }
        
        logger.info(f"\n📋 问题 {question_idx + 1}: {question_data['question'][:100]}...")
        logger.info(f"📍 正确答案: {question_data['answer']}")
        
        for role in self.roles:
            logger.info(f"\n🎭 使用角色: {self.role_names[role]} ({role})")
            
            try:
                # 评估单个问题（使用只有一个问题的数据集）
                start_time = time.time()
                
                # 格式化prompt
                formatted_prompt = processor.format_prompt(
                    question=question_data["question"],
                    template_name=role,
                    few_shot=self.config["few_shot"]
                )
                
                # 使用self-consistency进行预测
                predictions = []
                
                for _ in range(self.config["n_consistency_samples"]):
                    response = self.vllm_wrapper.generate(
                        prompt=formatted_prompt,
                        temperature=self.config["temperature"],
                        top_p=self.config["top_p"],
                        max_tokens=self.config["max_tokens"]
                    )
                    
                    # 提取答案
                    response_text = response.choices[0].message.content if hasattr(response, 'choices') else str(response)
                    predicted_answer = processor.extract_answer(response_text)
                    predictions.append(predicted_answer)
                
                # 使用多数投票
                vote_counts = Counter(predictions)
                predicted_answer = vote_counts.most_common(1)[0][0]
                
                # 检查正确性
                is_correct = processor.check_answer_correctness(predicted_answer, question_data["answer"])
                confidence_score = vote_counts[predicted_answer] / len(predictions)
                
                eval_time = time.time() - start_time
                
                # 记录结果
                results["roles"][role] = {
                    "predicted_answer": predicted_answer,
                    "is_correct": is_correct,
                    "confidence_score": confidence_score,
                    "reasoning_paths": predictions,
                    "evaluation_time": eval_time,
                    "role_name": self.role_names[role]
                }
                
                # 显示结果
                status = "✅ 正确" if is_correct else "❌ 错误"
                logger.info(f"   {status} | 预测: {predicted_answer} | 置信度: {confidence_score:.3f}")
                
            except Exception as e:
                logger.error(f"   ❌ 角色 {role} 评估失败: {e}")
                results["roles"][role] = {
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "confidence_score": 0.0,
                    "reasoning_paths": [],
                    "evaluation_time": 0.0,
                    "role_name": self.role_names[role],
                    "error": str(e)
                }
        
        return results
    
    def evaluate_dataset(self, processor, test_data: List[dict], output_dir: Path) -> Dict:
        """评估整个数据集"""
        
        logger.info(f"🚀 开始多角色对比评估")
        logger.info(f"📊 数据集大小: {len(test_data)} 个问题")
        logger.info(f"🎭 使用角色: {', '.join([self.role_names[r] for r in self.roles])}")
        
        all_results = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "total_questions": len(test_data),
                "roles": self.roles,
                "role_names": self.role_names,
                "evaluation_config": self.config
            },
            "questions": [],
            "summary": {}
        }
        
        # 逐题评估
        for idx, question_data in enumerate(test_data):
            logger.info(f"\n{'='*80}")
            logger.info(f"📋 处理问题 {idx + 1}/{len(test_data)}")
            
            question_result = self.evaluate_single_question(processor, question_data, idx)
            all_results["questions"].append(question_result)
            
            # 定期保存进度
            if (idx + 1) % 5 == 0 or idx == len(test_data) - 1:
                self.save_progress(all_results, output_dir, idx + 1, len(test_data))
        
        # 计算总结统计
        all_results["summary"] = self.calculate_summary_stats(all_results["questions"])
        
        return all_results
    
    def save_progress(self, results: Dict, output_dir: Path, current: int, total: int):
        """保存评估进度"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        progress_file = output_dir / f"role_comparison_progress_{current}of{total}_{timestamp}.json"
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 进度已保存: {current}/{total} ({current/total*100:.1f}%) -> {progress_file}")
    
    def calculate_summary_stats(self, questions: List[Dict]) -> Dict:
        """计算统计摘要"""
        
        summary = {
            "role_performance": {},
            "question_difficulty": {},
            "agreement_analysis": {}
        }
        
        # 角色表现统计
        for role in self.roles:
            correct_count = sum(1 for q in questions if q["roles"][role]["is_correct"])
            total_count = len(questions)
            accuracy = correct_count / total_count if total_count > 0 else 0
            
            avg_confidence = np.mean([q["roles"][role]["confidence_score"] for q in questions])
            avg_time = np.mean([q["roles"][role]["evaluation_time"] for q in questions])
            
            summary["role_performance"][role] = {
                "role_name": self.role_names[role],
                "accuracy": accuracy,
                "correct_count": correct_count,
                "total_count": total_count,
                "avg_confidence": avg_confidence,
                "avg_evaluation_time": avg_time
            }
        
        # 问题难度分析（基于有多少角色答对）
        for i, question in enumerate(questions):
            correct_roles = [role for role in self.roles if question["roles"][role]["is_correct"]]
            difficulty_level = len(correct_roles)  # 0=最难，4=最易
            
            summary["question_difficulty"][i] = {
                "question_id": i,
                "correct_roles_count": difficulty_level,
                "correct_roles": correct_roles,
                "difficulty_label": self.get_difficulty_label(difficulty_level)
            }
        
        # 角色间一致性分析
        summary["agreement_analysis"] = self.analyze_role_agreement(questions)
        
        return summary
    
    def get_difficulty_label(self, correct_count: int) -> str:
        """根据正确角色数量标注难度"""
        labels = {
            0: "极难（无角色正确）",
            1: "困难（1个角色正确）", 
            2: "中等（2个角色正确）",
            3: "简单（3个角色正确）",
            4: "极简单（全部角色正确）"
        }
        return labels.get(correct_count, "未知")
    
    def analyze_role_agreement(self, questions: List[Dict]) -> Dict:
        """分析角色间的一致性"""
        
        agreement = {}
        
        # 计算两两角色间的一致性
        for i, role1 in enumerate(self.roles):
            for j, role2 in enumerate(self.roles):
                if i >= j:
                    continue
                
                agreement_count = 0
                total_count = len(questions)
                
                for question in questions:
                    result1 = question["roles"][role1]["is_correct"]
                    result2 = question["roles"][role2]["is_correct"]
                    if result1 == result2:
                        agreement_count += 1
                
                agreement_rate = agreement_count / total_count if total_count > 0 else 0
                agreement[f"{role1}_vs_{role2}"] = {
                    "role1": self.role_names[role1],
                    "role2": self.role_names[role2], 
                    "agreement_rate": agreement_rate,
                    "agreement_count": agreement_count,
                    "total_count": total_count
                }
        
        return agreement

def create_visualization(results: Dict, output_dir: Path):
    """创建可视化图表"""
    
    logger.info("📊 创建可视化图表...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 角色表现对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('GSM8K多角色Prompt对比分析', fontsize=16, fontweight='bold')
    
    # 1.1 准确率对比
    roles = list(results["summary"]["role_performance"].keys())
    role_names = [results["summary"]["role_performance"][r]["role_name"] for r in roles]
    accuracies = [results["summary"]["role_performance"][r]["accuracy"] for r in roles]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    axes[0,0].bar(role_names, accuracies, color=colors)
    axes[0,0].set_title('各角色准确率对比')
    axes[0,0].set_ylabel('准确率')
    axes[0,0].set_ylim(0, 1)
    for i, v in enumerate(accuracies):
        axes[0,0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    # 1.2 置信度对比
    confidences = [results["summary"]["role_performance"][r]["avg_confidence"] for r in roles]
    
    axes[0,1].bar(role_names, confidences, color=colors)
    axes[0,1].set_title('各角色平均置信度')
    axes[0,1].set_ylabel('平均置信度')
    for i, v in enumerate(confidences):
        axes[0,1].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    # 1.3 问题难度分布
    difficulty_counts = {}
    for q_data in results["summary"]["question_difficulty"].values():
        level = q_data["difficulty_label"]
        difficulty_counts[level] = difficulty_counts.get(level, 0) + 1
    
    axes[1,0].pie(difficulty_counts.values(), labels=difficulty_counts.keys(), autopct='%1.1f%%')
    axes[1,0].set_title('问题难度分布')
    
    # 1.4 角色一致性热力图
    agreement_matrix = np.ones((len(roles), len(roles)))
    
    for key, data in results["summary"]["agreement_analysis"].items():
        role1, role2 = key.split("_vs_")
        i = roles.index(role1)
        j = roles.index(role2)
        agreement_rate = data["agreement_rate"]
        agreement_matrix[i, j] = agreement_rate
        agreement_matrix[j, i] = agreement_rate
    
    im = axes[1,1].imshow(agreement_matrix, cmap='RdYlBu', vmin=0, vmax=1)
    axes[1,1].set_xticks(range(len(role_names)))
    axes[1,1].set_yticks(range(len(role_names)))
    axes[1,1].set_xticklabels(role_names, rotation=45)
    axes[1,1].set_yticklabels(role_names)
    axes[1,1].set_title('角色间一致性')
    
    # 添加数值标注
    for i in range(len(roles)):
        for j in range(len(roles)):
            text = axes[1,1].text(j, i, f'{agreement_matrix[i, j]:.2f}',
                                ha="center", va="center", color="black")
    
    plt.colorbar(im, ax=axes[1,1])
    plt.tight_layout()
    
    # 保存图表
    chart_file = output_dir / f"role_comparison_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    logger.info(f"📊 图表已保存: {chart_file}")
    
    # 2. 详细的对错分布图
    create_detailed_correctness_chart(results, output_dir)
    
    # 关闭图表以释放内存
    plt.close()

def create_detailed_correctness_chart(results: Dict, output_dir: Path):
    """创建详细的对错分布图"""
    
    questions = results["questions"]
    roles = results["metadata"]["roles"]
    role_names = [results["metadata"]["role_names"][r] for r in roles]
    
    # 创建对错矩阵（问题 x 角色）
    correctness_matrix = np.zeros((len(questions), len(roles)))
    
    for i, question in enumerate(questions):
        for j, role in enumerate(roles):
            correctness_matrix[i, j] = 1 if question["roles"][role]["is_correct"] else 0
    
    # 绘制热力图
    plt.figure(figsize=(12, max(8, len(questions) * 0.2)))
    
    sns.heatmap(correctness_matrix, 
                xticklabels=role_names,
                yticklabels=[f"Q{i+1}" for i in range(len(questions))],
                cmap='RdYlGn', 
                cbar_kws={'label': '正确性 (1=正确, 0=错误)'},
                linewidths=0.5)
    
    plt.title('各角色在每道题上的正确性分布', fontsize=14, fontweight='bold')
    plt.xlabel('角色类型')
    plt.ylabel('问题编号')
    plt.xticks(rotation=45)
    
    # 保存详细图表
    detail_chart = output_dir / f"detailed_correctness_matrix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(detail_chart, dpi=300, bbox_inches='tight')
    logger.info(f"📊 详细分布图已保存: {detail_chart}")
    
    plt.tight_layout()
    # 关闭图表以释放内存
    plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="GSM8K多角色对比评估")
    
    # 模型配置
    parser.add_argument("--model", default="Mistral-7B-Instruct-v0.3", help="模型名称")
    parser.add_argument("--base_url", default="http://localhost:3333", help="vLLM服务器地址")
    
    # 评估配置
    parser.add_argument("--num_samples", type=int, default=20, help="评估样本数量")
    parser.add_argument("--few_shot", type=int, default=3, help="Few-shot示例数量")
    parser.add_argument("--n_consistency_samples", type=int, default=1, help="Self-consistency采样次数")
    parser.add_argument("--temperature", type=float, default=0.7, help="采样温度")
    parser.add_argument("--top_p", type=float, default=0.9, help="Top-p采样")
    parser.add_argument("--max_tokens", type=int, default=512, help="最大生成token数")
    parser.add_argument("--confidence_temp", type=float, default=0.2, help="置信度温度")
    
    # 输出配置
    parser.add_argument("--output_dir", default="../results/role_comparison", help="结果输出目录")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info("🚀 开始GSM8K多角色对比评估")
    logger.info(f"📊 配置信息:")
    logger.info(f"   - 模型: {args.model}")
    logger.info(f"   - API地址: {args.base_url}")
    logger.info(f"   - 评估样本数: {args.num_samples}")
    logger.info(f"   - Few-shot: {args.few_shot}")
    logger.info(f"   - Consistency采样: {args.n_consistency_samples}")
    
    # 创建vLLM包装器
    try:
        config = VLLMConfig(
            base_url=args.base_url,
            model_name=args.model
        )
        vllm_wrapper = VLLMModelWrapper(config)
        logger.info("✅ vLLM连接成功")
    except Exception as e:
        logger.error(f"❌ vLLM连接失败: {e}")
        return
    
    # 加载数据集
    processor = get_local_dataset_processor("GSM8K")
    test_data = sample_local_dataset(processor, args.num_samples)
    logger.info(f"📈 加载 {len(test_data)} 个测试样本")
    
    # 创建评估配置
    evaluation_config = {
        "few_shot": args.few_shot,
        "n_consistency_samples": args.n_consistency_samples,
        "temperature": args.temperature,
        "top_p": args.top_p,
        "max_tokens": args.max_tokens,
        "confidence_temp": args.confidence_temp,
        "use_cot": True,
        "use_path_confidence": False,
        "combined_confidence_threshold": 0.0
    }
    
    # 创建评估器并运行评估
    evaluator = RoleComparisonEvaluator(vllm_wrapper, evaluation_config)
    results = evaluator.evaluate_dataset(processor, test_data, output_dir)
    
    # 保存最终结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_results_file = output_dir / f"role_comparison_final_{timestamp}.json"
    
    with open(final_results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"💾 最终结果已保存: {final_results_file}")
    
    # 创建可视化
    create_visualization(results, output_dir)
    
    # 打印摘要
    logger.info("\n📋 评估摘要:")
    for role in results["metadata"]["roles"]:
        perf = results["summary"]["role_performance"][role]
        logger.info(f"   {perf['role_name']}: {perf['accuracy']:.3f} ({perf['correct_count']}/{perf['total_count']})")
    
    logger.info(f"\n✅ 多角色对比评估完成！")
    logger.info(f"📁 结果保存在: {output_dir}")

if __name__ == "__main__":
    main()
