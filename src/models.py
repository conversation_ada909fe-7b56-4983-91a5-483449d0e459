import torch
import json
import os
import time
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    """模型配置类"""
    name: str
    model_name: str
    max_length: int = 4096
    device: str = "auto"
    gpu_memory_fraction: float = 0.9  # GPU内存使用比例
    
    @staticmethod
    def setup_gpu_environment(gpu_ids: Optional[List[int]] = None):
        """设置GPU环境"""
        if gpu_ids is None:
            # 自动检测并使用所有可用GPU
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                print(f"🔍 检测到 {gpu_count} 个可用GPU:")
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
                
                os.environ["CUDA_VISIBLE_DEVICES"] = ",".join(str(i) for i in range(gpu_count))
                print(f"✅ 设置使用所有GPU: {os.environ['CUDA_VISIBLE_DEVICES']}")
            else:
                print("⚠️  未检测到可用GPU，将使用CPU")
                os.environ["CUDA_VISIBLE_DEVICES"] = ""
        else:
            # 使用指定的GPU
            gpu_list = ",".join(str(gpu) for gpu in gpu_ids)
            os.environ["CUDA_VISIBLE_DEVICES"] = gpu_list
            print(f"✅ 设置使用指定GPU: {gpu_list}")
            
            if torch.cuda.is_available():
                for i, gpu_id in enumerate(gpu_ids):
                    if gpu_id < torch.cuda.device_count():
                        gpu_name = torch.cuda.get_device_name(gpu_id)
                        gpu_memory = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
                        print(f"   GPU {gpu_id}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        # 设置GPU内存管理
        if torch.cuda.is_available() and os.environ.get("CUDA_VISIBLE_DEVICES", "") != "":
            torch.cuda.empty_cache()  # 清空缓存
            print(f"🔧 GPU内存已清空，当前可用: {torch.cuda.device_count()} 个设备")

@dataclass
class InferenceConfig:
    """推理配置类"""
    num_samples: int = 5
    temperature: float = 0.7
    top_p: float = 0.9
    max_new_tokens: int = 512
    do_sample: bool = True

class ModelWrapper:
    """统一模型接口包装器"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.load_model()
    
    def load_model(self):
        """加载模型和tokenizer"""
        logger.info(f"Loading model: {self.config.model_name}")
        
        try:
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                torch_dtype=torch.float16,
                device_map=self.config.device,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            self.model.config._attn_implementation = "eager" 
            logger.info(f"Model {self.config.name} loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model {self.config.name}: {e}")
            raise
    
    def generate_response(self, prompt: str, inference_config: InferenceConfig) -> str:
        """生成单个响应"""
        inputs = self.tokenizer(prompt, return_tensors="pt", padding=True)
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=inference_config.max_new_tokens,
                temperature=inference_config.temperature,
                top_p=inference_config.top_p,
                do_sample=inference_config.do_sample,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
        
        # 解码输出，只返回生成的新token
        generated_tokens = outputs[0][inputs["input_ids"].shape[1]:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        
        return response.strip()
    
    def generate_multiple_responses(self, prompt: str, inference_config: InferenceConfig) -> List[str]:
        """生成多个响应用于self-consistency"""
        responses = []
        for i in range(inference_config.num_samples):
            try:
                response = self.generate_response(prompt, inference_config)
                responses.append(response)
                logger.debug(f"Generated response {i+1}/{inference_config.num_samples}")
            except Exception as e:
                logger.error(f"Error generating response {i+1}: {e}")
                responses.append("")
        
        return responses
    
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        return len(self.tokenizer.encode(text))

# 预定义模型配置
MODEL_CONFIGS = {
    "ministral-7b": ModelConfig(
        name="ministral-7b",
        model_name="/data/Mistral-7B-Instruct-v0.3"
    ),
    "gemma-2-2b": ModelConfig(
        name="gemma-2-2b", 
        model_name="/data/gemma-2-2b"
    ),
    "qwen2.5-3b": ModelConfig(
        name="qwen2.5-3b",
        model_name="/data/Qwen2.5-3B-Instruct"
    )
}

def get_model(model_name: str) -> ModelWrapper:
    """获取模型实例"""
    if model_name not in MODEL_CONFIGS:
        raise ValueError(f"Unsupported model: {model_name}")
    
    config = MODEL_CONFIGS[model_name]
    return ModelWrapper(config)
