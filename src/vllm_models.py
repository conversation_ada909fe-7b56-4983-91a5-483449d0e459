"""
vLLM API客户端模型包装器
通过HTTP API调用vLLM部署的模型进行推理
"""

import requests
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class VLLMConfig:
    """vLLM API配置"""
    base_url: str = "http://localhost:8000"  # vLLM服务器地址
    model_name: str = ""  # 模型名称
    api_key: Optional[str] = None  # API密钥（如果需要）
    timeout: int = 300  # 请求超时时间（秒）
    max_retries: int = 3  # 最大重试次数

class VLLMModelWrapper:
    """vLLM API模型包装器"""
    
    def __init__(self, config: VLLMConfig):
        self.config = config
        self.session = requests.Session()
        
        # 设置headers
        self.headers = {
            "Content-Type": "application/json"
        }
        
        if config.api_key:
            self.headers["Authorization"] = f"Bearer {config.api_key}"
        
        # 测试连接
        self._test_connection()

    
     ## NEW ##
    def generate_with_logprobs(self, prompt: str, **kwargs) -> Tuple[str, Dict[str, float]]:
        """
        生成单个token并返回其logprobs，用于置信度计算。
        
        Args:
            prompt: 输入提示
            **kwargs: 生成参数
        
        Returns:
            A tuple of (generated_token, logprobs_dict)
        """
        data = {
            "model": self.config.model_name,
            "prompt": prompt,
            "max_tokens": 1,  # 我们只需要一个token (e.g., "True" or "False")
            "logprobs": 15,    # 返回概率最高的5个token的logprobs
            "temperature": kwargs.get("temperature", 0.0), # 通常置信度评估用0温
            "stream": False,
        }

        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(
                    f"{self.config.base_url}/v1/completions",
                    headers=self.headers,
                    json=data,
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    choices = result.get("choices", [])
                    if choices and "logprobs" in choices[0] and choices[0]["logprobs"] is not None:
                        generated_token = choices[0].get("text", "").strip()
                        # vLLM 返回的 logprobs 结构: {"tokens": [...], "token_logprobs": [...], "top_logprobs": [{...}]}
                        top_logprobs = choices[0]["logprobs"]["top_logprobs"][0]

                        return generated_token, top_logprobs
                    else:
                        raise ValueError("No valid logprobs in response")
                else:
                    # ... (error handling as in _single_generate) ...
                    error_msg = f"API request failed with status {response.status_code}: {response.text}"
                    if attempt < self.config.max_retries - 1:
                        logger.warning(f"⚠️ {error_msg}, 重试中...")
                        time.sleep(2 ** attempt)
                        continue
                    else:
                        raise requests.RequestException(error_msg)
            except Exception as e:
                # ... (error handling as in _single_generate) ...
                if attempt < self.config.max_retries - 1:
                    logger.warning(f"⚠️ Request failed: {e}, 重试中...")
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise e
        
        raise RuntimeError("All retry attempts failed for generate_with_logprobs")

    def _test_connection(self):
        """测试vLLM服务器连接"""
        try:
            # 尝试获取模型列表
            response = self.session.get(
                f"{self.config.base_url}/v1/models",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                models = response.json()
                logger.info(f"✅ 成功连接到vLLM服务器: {self.config.base_url}")
                logger.info(f"📋 可用模型: {[m.get('id', 'unknown') for m in models.get('data', [])]}")
            else:
                logger.warning(f"⚠️ vLLM服务器响应异常: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 无法连接到vLLM服务器 {self.config.base_url}: {e}")
            raise ConnectionError(f"Cannot connect to vLLM server: {e}")
    
    def generate(self, prompts: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        批量生成文本
        
        Args:
            prompts: 输入提示列表
            **kwargs: 生成参数（temperature, top_p, max_tokens等）
        
        Returns:
            生成结果列表
        """
        if isinstance(prompts, str):
            prompts = [prompts]
        
        results = []
        
        for prompt in prompts:
            result = self._single_generate(prompt, **kwargs)
            results.append(result)
        
        return results
    
    def _single_generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        单次生成
        
        Args:
            prompt: 输入提示
            **kwargs: 生成参数
        
        Returns:
            生成结果
        """
        # 准备请求数据
        data = {
            "model": self.config.model_name,
            "prompt": prompt,
            "max_tokens": kwargs.get("max_tokens", kwargs.get("max_new_tokens", 512)),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 0.9),
            "n": kwargs.get("n", 1),
            "stream": False,
            "stop": kwargs.get("stop", None)
        }
        
        # 添加其他参数
        if "frequency_penalty" in kwargs:
            data["frequency_penalty"] = kwargs["frequency_penalty"]
        if "presence_penalty" in kwargs:
            data["presence_penalty"] = kwargs["presence_penalty"]
        
        # 执行请求（带重试）
        for attempt in range(self.config.max_retries):
            try:
                start_time = time.time()
                
                response = self.session.post(
                    f"{self.config.base_url}/v1/completions",
                    headers=self.headers,
                    json=data,
                    timeout=self.config.timeout
                )
                
                inference_time = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 提取生成的文本和token数量
                    choices = result.get("choices", [])
                    if choices:
                        generated_text = choices[0].get("text", "")
                        usage = result.get("usage", {})
                        
                        return {
                            "generated_text": generated_text,
                            "prompt_tokens": usage.get("prompt_tokens", 0),
                            "completion_tokens": usage.get("completion_tokens", 0),
                            "total_tokens": usage.get("total_tokens", 0),
                            "inference_time": inference_time,
                            "raw_response": result
                        }
                    else:
                        raise ValueError("No choices in response")
                
                else:
                    error_msg = f"API request failed with status {response.status_code}: {response.text}"
                    if attempt < self.config.max_retries - 1:
                        logger.warning(f"⚠️ {error_msg}, 重试中... ({attempt + 1}/{self.config.max_retries})")
                        time.sleep(2 ** attempt)  # 指数退避
                        continue
                    else:
                        raise requests.RequestException(error_msg)
                        
            except requests.Timeout:
                error_msg = f"Request timeout after {self.config.timeout} seconds"
                if attempt < self.config.max_retries - 1:
                    logger.warning(f"⚠️ {error_msg}, 重试中... ({attempt + 1}/{self.config.max_retries})")
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise requests.RequestException(error_msg)
                    
            except Exception as e:
                error_msg = f"Request failed: {e}"
                if attempt < self.config.max_retries - 1:
                    logger.warning(f"⚠️ {error_msg}, 重试中... ({attempt + 1}/{self.config.max_retries})")
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise requests.RequestException(error_msg)
        
        raise RuntimeError("All retry attempts failed")
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            response = self.session.get(
                f"{self.config.base_url}/v1/models",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                models = response.json()
                # 查找指定模型的信息
                for model in models.get("data", []):
                    if model.get("id") == self.config.model_name:
                        return model
                        
                # 如果没有找到指定模型，返回第一个模型信息
                if models.get("data"):
                    return models["data"][0]
                    
            return {"id": self.config.model_name, "object": "model"}
            
        except Exception as e:
            logger.warning(f"无法获取模型信息: {e}")
            return {"id": self.config.model_name, "object": "model"}

class VLLMSelfConsistencyWrapper:
    """vLLM Self-Consistency包装器"""
    
    def __init__(self, vllm_wrapper: VLLMModelWrapper):
        self.vllm_wrapper = vllm_wrapper
    

    # def generate_multiple(self, prompt: str, n_samples: int = 5, **kwargs) -> List[Dict[str, Any]]:
    #     """
    #     一次性生成多个样本用于self-consistency，并正确解析响应。
    #     """
    #     # 准备请求数据，明确设置 n = n_samples
    #     data = {
    #         "model": self.vllm_wrapper.config.model_name,
    #         "prompt": prompt,
    #         "max_tokens": kwargs.get("max_tokens", kwargs.get("max_new_tokens", 512)),
    #         "temperature": kwargs.get("temperature", 0.7),
    #         "top_p": kwargs.get("top_p", 0.9),
    #         "n": n_samples,
    #         "stream": False,
    #         "stop": kwargs.get("stop")
    #     }

    #     try:
    #         start_time = time.time()
    #         # 发送一次API请求
    #         response_json = self.vllm_wrapper._make_request(data)
    #         inference_time = time.time() - start_time
            
    #         # 正确解析返回的多个 choices
    #         choices = response_json.get("choices", [])
    #         usage = response_json.get("usage", {})
    #         total_prompt_tokens = usage.get("prompt_tokens", 0)
    #         total_completion_tokens = usage.get("completion_tokens", 0)
            
    #         results = []
            
    #         # 遍历返回的每一个 choice
    #         for i, choice in enumerate(choices):
    #             # completion_tokens 的近似计算（vLLM目前不在每个choice中返回token数）
    #             avg_completion_tokens = total_completion_tokens / len(choices) if choices else 0
                
    #             results.append({
    #                 "generated_text": choice.get("text", ""),
    #                 "prompt_tokens": total_prompt_tokens, # prompt 对所有样本都是一样的
    #                 "completion_tokens": avg_completion_tokens,
    #                 "total_tokens": total_prompt_tokens + avg_completion_tokens,
    #                 "inference_time": inference_time / len(choices) if choices else 0, # 近似
    #                 "sample_id": i
    #             })
            
    #         # 如果返回的 choices 数量少于请求的数量，打印警告
    #         if len(results) < n_samples:
    #             logger.warning(f"请求了 {n_samples} 个样本，但只收到了 {len(results)} 个。")

    #         return results
            
    #     except Exception as e:
    #         logger.error(f"Self-consistency生成失败: {e}", exc_info=True)
    #         # 在失败时返回一个空列表或根据需要进行错误处理
    #         return []
    




    #new implementation2
    # def _generate_completions(self, prompt: str, **kwargs) -> List[Dict[str, Any]]:
    #     """
    #     【已修复】为单个prompt生成一个或多个completion，并正确返回列表。
    #     """
    #     # 准备请求数据
    #     data = {
    #         "model": self.config.model_name,
    #         "prompt": prompt,
    #         "max_tokens": kwargs.get("max_tokens", kwargs.get("max_new_tokens", 512)),
    #         "temperature": kwargs.get("temperature", 0.7),
    #         "top_p": kwargs.get("top_p", 0.9),
    #         "n": kwargs.get("n", 1),
    #         "stream": False,
    #         "stop": kwargs.get("stop", None)
    #     }
        
    #     # 添加其他参数
    #     if "frequency_penalty" in kwargs: data["frequency_penalty"] = kwargs["frequency_penalty"]
    #     if "presence_penalty" in kwargs: data["presence_penalty"] = kwargs["presence_penalty"]
        
    #     # 执行请求（带重试）
    #     for attempt in range(self.config.max_retries):
    #         try:
    #             start_time = time.time()
    #             response = self.session.post(
    #                 f"{self.config.base_url}/v1/completions",
    #                 headers=self.headers,
    #                 json=data,
    #                 timeout=self.config.timeout
    #             )
    #             inference_time = time.time() - start_time
                
    #             if response.status_code == 200:
    #                 result = response.json()
    #                 choices = result.get("choices", [])
    #                 usage = result.get("usage", {})
                    
    #                 if not choices:
    #                     # 这是一个有效的响应，但没有内容，返回空列表
    #                     logger.warning(f"API为prompt返回了空的choices列表: '{prompt[:100]}...'")
    #                     return []
                    
    #                 # --- 这是最核心的修复 ---
    #                 # 1. 准备一个列表来存放所有结果
    #                 completions_list = []
    #                 num_choices = len(choices)
    #                 total_completion_tokens = usage.get("completion_tokens", 0)
                    
    #                 # 2. 遍历vLLM返回的每一个choice
    #                 for i, choice in enumerate(choices):
    #                     # 3. 为每个choice创建独立的、信息准确的字典
    #                     # 注意：vLLM的usage是总的，所以我们这里只能做近似分配
    #                     # 但这远比之前错误分割prompt token要好
    #                     completions_list.append({
    #                         "generated_text": choice.get("text", ""),
    #                         # prompt_tokens只在第一个结果中计算，避免重复计数
    #                         "prompt_tokens": usage.get("prompt_tokens", 0) if i == 0 else 0,
    #                         # 近似分配completion_tokens
    #                         "completion_tokens": total_completion_tokens // num_choices if num_choices > 0 else 0,
    #                         "inference_time": inference_time / num_choices if num_choices > 0 else 0,
    #                         "raw_choice": choice 
    #                     })
                    
    #                 # 4. 返回包含所有结果的列表
    #                 return completions_list
    #                 # --- 修复结束 ---
    #             else:
    #                 error_msg = f"API请求失败，状态码 {response.status_code}: {response.text}"
    #                 if attempt < self.config.max_retries - 1:
    #                     logger.warning(f"⚠️ {error_msg}, 重试中...")
    #                     time.sleep(2 ** attempt)
    #                     continue
    #                 raise requests.RequestException(error_msg)
    #         except Exception as e:
    #             error_msg = f"请求失败: {e}"
    #             if attempt < self.config.max_retries - 1:
    #                 logger.warning(f"⚠️ {error_msg}, 重试中...")
    #                 time.sleep(2 ** attempt)
    #                 continue
    #             raise e
    #     raise RuntimeError("所有重试均失败")
    
    def generate_multiple(self, prompt: str, n_samples: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        生成多个样本用于self-consistency
        
        Args:
            prompt: 输入提示
            n_samples: 生成样本数量
            **kwargs: 生成参数
        
        Returns:
            生成结果列表
        """
        # 设置采样参数
        generation_kwargs = {
            "max_tokens": kwargs.get("max_tokens", kwargs.get("max_new_tokens", 512)),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 0.9),
            "n": n_samples  # 一次性生成多个样本
        }
        
        try:
            # 使用vLLM的批量生成功能
            response = self.vllm_wrapper._single_generate(prompt, **generation_kwargs)
            
            # 如果API支持一次生成多个样本
            if isinstance(response.get("raw_response", {}).get("choices"), list):
                choices = response["raw_response"]["choices"]
                results = []
                
                total_prompt_tokens = response.get("prompt_tokens", 0)
                total_completion_tokens = response.get("completion_tokens", 0)
                avg_tokens_per_sample = total_completion_tokens // max(len(choices), 1)
                
                for i, choice in enumerate(choices):
                    results.append({
                        "generated_text": choice.get("text", ""),
                        "prompt_tokens": total_prompt_tokens // len(choices) if len(choices) > 0 else 0,
                        "completion_tokens": avg_tokens_per_sample,
                        "total_tokens": (total_prompt_tokens // len(choices) + avg_tokens_per_sample) if len(choices) > 0 else 0,
                        "inference_time": response.get("inference_time", 0) / len(choices) if len(choices) > 0 else 0,
                        "sample_id": i
                    })
                
                return results
            
            else:
                # 如果API不支持批量生成，逐个生成
                results = []
                for i in range(n_samples):
                    single_kwargs = generation_kwargs.copy()
                    single_kwargs["n"] = 1
                    
                    result = self.vllm_wrapper._single_generate(prompt, **single_kwargs)
                    result["sample_id"] = i
                    results.append(result)
                
                return results
                
        except Exception as e:
            logger.error(f"Self-consistency生成失败: {e}")
            raise


    ##new implementation2
    # def generate_multiple(self, prompt: str, n_samples: int = 5, **kwargs) -> List[Dict[str, Any]]:
    #     """
    #     【已简化】为单个prompt生成多个样本，现在直接调用修复后的函数。
    #     """
    #     # 设置采样参数，特别是 'n'
    #     generation_kwargs = {
    #         "max_tokens": kwargs.get("max_tokens", kwargs.get("max_new_tokens", 512)),
    #         "temperature": kwargs.get("temperature", 0.7),
    #         "top_p": kwargs.get("top_p", 0.9),
    #         "n": n_samples
    #     }
        
    #     try:
    #         # --- 这是最核心的简化 ---
    #         # 直接调用我们刚刚修复的、能正确返回列表的函数
    #         # 不再需要任何复杂的补丁和解包逻辑
    #         results = self.vllm_wrapper._generate_completions(prompt, **generation_kwargs)
            
    #         # 为了兼容 evaluator，为每个结果添加 'sample_id' 和 'total_tokens'
    #         for i, res in enumerate(results):
    #             res["total_tokens"] = res.get("prompt_tokens", 0) + res.get("completion_tokens", 0)
    #             res["sample_id"] = i
            
    #         return results
    #         # --- 简化结束 ---
                
    #     except Exception as e:
    #         logger.error(f"Self-consistency生成失败: {e}")
    #         raise

def create_vllm_models_config() -> Dict[str, VLLMConfig]:
    """创建预定义的vLLM模型配置"""
    return {
        "ministral-8b": VLLMConfig(
            base_url="http://localhost:8888",
            model_name="ministral-8b-instruct-2410"
        ),
        "gemma-2-2b": VLLMConfig(
            base_url="http://localhost:8889",
            model_name="gemma-2-2b"
        ),
        "qwen2.5-3b": VLLMConfig(
            base_url="http://localhost:8890", 
            model_name="qwen2.5-3b"
        )
    }

def test_vllm_connection(base_url: str = "http://localhost:8000") -> bool:
    """测试vLLM服务器连接"""
    try:
        response = requests.get(f"{base_url}/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ vLLM服务器连接成功: {base_url}")
            print(f"📋 可用模型: {[m.get('id', 'unknown') for m in models.get('data', [])]}")
            return True
        else:
            print(f"❌ vLLM服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到vLLM服务器: {e}")
        return False
