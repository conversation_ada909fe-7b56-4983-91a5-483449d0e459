import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class EvaluationSettings:
    """评估设置配置"""
    num_samples_per_dataset: Optional[int]
    description: str

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """获取模型配置"""
        if model_name not in self.config['models']:
            raise ValueError(f"未知模型: {model_name}")
        return self.config['models'][model_name]
    
    def get_dataset_config(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据集配置"""
        if dataset_name not in self.config['datasets']:
            raise ValueError(f"未知数据集: {dataset_name}")
        return self.config['datasets'][dataset_name]
    
    def get_inference_config(self, config_name: str = "default") -> Dict[str, Any]:
        """获取推理配置"""
        if config_name not in self.config['inference_config']:
            raise ValueError(f"未知推理配置: {config_name}")
        return self.config['inference_config'][config_name]
    
    def get_evaluation_settings(self, setting_name: str) -> EvaluationSettings:
        """获取评估设置"""
        if setting_name not in self.config['evaluation_settings']:
            raise ValueError(f"未知评估设置: {setting_name}")
        
        setting = self.config['evaluation_settings'][setting_name]
        return EvaluationSettings(
            num_samples_per_dataset=setting['num_samples_per_dataset'],
            description=setting['description']
        )
    
    def get_available_models(self) -> list:
        """获取所有可用模型"""
        return list(self.config['models'].keys())
    
    def get_available_datasets(self) -> list:
        """获取所有可用数据集"""
        return list(self.config['datasets'].keys())
    
    def get_available_inference_configs(self) -> list:
        """获取所有可用推理配置"""
        return list(self.config['inference_config'].keys())
    
    def get_available_evaluation_settings(self) -> list:
        """获取所有可用评估设置"""
        return list(self.config['evaluation_settings'].keys())
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("配置摘要:")
        print("=" * 50)
        
        print("可用模型:")
        for model_name, config in self.config['models'].items():
            print(f"  - {model_name}: {config['description']}")
        
        print("\n可用数据集:")
        for dataset_name, config in self.config['datasets'].items():
            print(f"  - {dataset_name}: {config['description']}")
        
        print("\n推理配置:")
        for config_name, config in self.config['inference_config'].items():
            print(f"  - {config_name}: {config['description']}")
        
        print("\n评估设置:")
        for setting_name, setting in self.config['evaluation_settings'].items():
            samples = setting['num_samples_per_dataset'] or "全部"
            print(f"  - {setting_name}: {setting['description']} (样本数: {samples})")

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
