import json
import time
from collections import Counter
from typing import List, Dict, Any, Tu<PERSON>, Optional
from dataclasses import dataclass, asdict
import logging

from .models import ModelWrapper, InferenceConfig
from .datasets import DatasetProcessor

logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """单个样本的评估结果"""
    question_id: int
    question: str
    gold_answer: str
    responses: List[str]
    extracted_answers: List[str]
    final_answer: str
    is_correct: bool
    total_tokens: int
    inference_time: float

@dataclass
class ModelResults:
    """模型在数据集上的整体结果"""
    model_name: str
    dataset_name: str
    accuracy: float
    total_samples: int
    correct_samples: int
    avg_tokens_per_sample: float
    total_tokens: int
    avg_inference_time: float
    total_inference_time: float
    sample_results: List[EvaluationResult]

class SelfConsistencyEvaluator:
    """Self-Consistency评估器"""
    
    def __init__(self, model: ModelWrapper, inference_config: InferenceConfig):
        self.model = model
        self.inference_config = inference_config
    
    def evaluate_sample(self, example: Dict[str, Any], processor: DatasetProcessor, 
                       question_id: int) -> EvaluationResult:
        """评估单个样本"""
        start_time = time.time()
        
        # 格式化prompt
        prompt = processor.format_prompt(example)
        
        # 生成多个响应
        responses = self.model.generate_multiple_responses(prompt, self.inference_config)
        
        # 从每个响应中提取答案
        extracted_answers = []
        for response in responses:
            answer = processor.extract_answer(response)
            extracted_answers.append(answer)
        
        # Self-consistency: 选择最频繁的答案
        final_answer = self._get_majority_answer(extracted_answers)
        
        # 检查答案正确性
        gold_answer = example.get("answer", "")
        is_correct = processor.check_answer(final_answer, gold_answer)
        
        # 计算token数
        total_tokens = sum(self.model.count_tokens(prompt + response) for response in responses)
        
        inference_time = time.time() - start_time
        
        return EvaluationResult(
            question_id=question_id,
            question=example.get("question", ""),
            gold_answer=gold_answer,
            responses=responses,
            extracted_answers=extracted_answers,
            final_answer=final_answer,
            is_correct=is_correct,
            total_tokens=total_tokens,
            inference_time=inference_time
        )
    
    def _get_majority_answer(self, answers: List[str]) -> str:
        """获取众数答案（Self-Consistency核心逻辑）"""
        # 过滤掉空答案
        valid_answers = [ans for ans in answers if ans.strip()]
        
        if not valid_answers:
            return ""
        
        # 计算答案频次
        counter = Counter(valid_answers)
        
        # 返回最频繁的答案
        return counter.most_common(1)[0][0]
    
    def evaluate_dataset(self, processor: DatasetProcessor, 
                        num_samples: Optional[int] = None) -> ModelResults:
        """评估整个数据集"""
        logger.info(f"Starting evaluation of {processor.dataset_name} with {self.model.config.name}")
        
        # 检查数据集是否加载成功
        if processor.dataset is None:
            raise ValueError(f"Dataset {processor.dataset_name} is not loaded")
        
        # 采样数据
        if num_samples is None:
            samples = list(processor.dataset)
        else:
            samples = list(processor.dataset)[:num_samples]
        
        sample_results = []
        correct_count = 0
        total_tokens = 0
        total_time = 0
        
        for i, example in enumerate(samples):
            logger.info(f"Evaluating sample {i+1}/{len(samples)}")
            
            try:
                result = self.evaluate_sample(example, processor, i)
                sample_results.append(result)
                
                if result.is_correct:
                    correct_count += 1
                
                total_tokens += result.total_tokens
                total_time += result.inference_time
                
                # 每10个样本输出一次进度
                if (i + 1) % 10 == 0:
                    current_acc = correct_count / (i + 1)
                    logger.info(f"Progress: {i+1}/{len(samples)}, Current accuracy: {current_acc:.4f}")
                
            except Exception as e:
                logger.error(f"Error evaluating sample {i}: {e}")
                continue
        
        # 计算整体统计
        accuracy = correct_count / len(sample_results) if sample_results else 0
        avg_tokens = total_tokens / len(sample_results) if sample_results else 0
        avg_time = total_time / len(sample_results) if sample_results else 0
        
        return ModelResults(
            model_name=self.model.config.name,
            dataset_name=processor.dataset_name,
            accuracy=accuracy,
            total_samples=len(sample_results),
            correct_samples=correct_count,
            avg_tokens_per_sample=avg_tokens,
            total_tokens=total_tokens,
            avg_inference_time=avg_time,
            total_inference_time=total_time,
            sample_results=sample_results
        )

def save_results(results: ModelResults, output_path: str):
    """保存评估结果到文件"""
    # 转换为可序列化的格式
    results_dict = asdict(results)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results_dict, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Results saved to {output_path}")

def load_results(file_path: str) -> ModelResults:
    """从文件加载评估结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 重构EvaluationResult对象
    sample_results = []
    for sample_data in data['sample_results']:
        sample_results.append(EvaluationResult(**sample_data))
    
    data['sample_results'] = sample_results
    
    return ModelResults(**data)
