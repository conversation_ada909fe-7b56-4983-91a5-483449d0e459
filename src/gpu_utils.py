"""
GPU工具模块 - 统一管理GPU设置和内存优化
"""

import os
import torch
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

def setup_gpu_environment(gpu_ids: Optional[List[int]] = None, memory_fraction: float = 0.9):
    """
    设置GPU环境
    
    Args:
        gpu_ids: 指定使用的GPU ID列表，None表示自动检测使用所有GPU
        memory_fraction: GPU内存使用比例
    """
    print("🔧 设置GPU环境...")
    
    if gpu_ids is None:
        # 自动检测并使用所有可用GPU
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"🔍 检测到 {gpu_count} 个可用GPU:")
            
            total_memory = 0
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                total_memory += gpu_memory
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            print(f"📊 总GPU内存: {total_memory:.1f} GB")
            os.environ["CUDA_VISIBLE_DEVICES"] = ",".join(str(i) for i in range(gpu_count))
            print(f"✅ 设置使用所有GPU: {os.environ['CUDA_VISIBLE_DEVICES']}")
            
        else:
            print("⚠️  未检测到可用GPU，将使用CPU")
            os.environ["CUDA_VISIBLE_DEVICES"] = ""
            return False
    else:
        # 使用指定的GPU
        if isinstance(gpu_ids, int):
            gpu_ids = [gpu_ids]
        
        gpu_list = ",".join(str(gpu) for gpu in gpu_ids)
        os.environ["CUDA_VISIBLE_DEVICES"] = gpu_list
        print(f"✅ 设置使用指定GPU: {gpu_list}")
        
        if torch.cuda.is_available():
            total_memory = 0
            for gpu_id in gpu_ids:
                if gpu_id < torch.cuda.device_count():
                    gpu_name = torch.cuda.get_device_name(gpu_id)
                    gpu_memory = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
                    total_memory += gpu_memory
                    print(f"   GPU {gpu_id}: {gpu_name} ({gpu_memory:.1f} GB)")
                else:
                    print(f"⚠️  GPU {gpu_id} 不存在，跳过")
            
            print(f"📊 可用GPU内存: {total_memory:.1f} GB")
    
    # 设置GPU内存管理
    if torch.cuda.is_available() and os.environ.get("CUDA_VISIBLE_DEVICES", "") != "":
        # 清空GPU缓存
        torch.cuda.empty_cache()
        
        # 设置内存分配策略
        torch.cuda.set_per_process_memory_fraction(memory_fraction)
        
        print(f"🔧 GPU内存管理:")
        print(f"   - 内存使用比例: {memory_fraction * 100:.0f}%")
        print(f"   - 可用设备数: {torch.cuda.device_count()}")
        
        # 显示当前内存使用情况
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            print(f"   - GPU {i}: 已分配 {allocated:.2f} GB, 已保留 {reserved:.2f} GB")
        
        return True
    
    return False

def get_optimal_batch_size(model_size_gb: float, sequence_length: int = 512, safety_factor: float = 0.7):
    """
    根据模型大小和可用GPU内存计算最优批处理大小
    
    Args:
        model_size_gb: 模型大小(GB)
        sequence_length: 序列长度
        safety_factor: 安全因子，预留内存比例
    
    Returns:
        推荐的批处理大小
    """
    if not torch.cuda.is_available():
        return 1
    
    # 获取第一个GPU的内存信息
    device = torch.cuda.current_device()
    total_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
    allocated_memory = torch.cuda.memory_allocated(device) / 1024**3
    available_memory = (total_memory - allocated_memory) * safety_factor
    
    # 估算每个样本的内存需求 (模型参数 + 激活值 + 梯度)
    # 简化估算：sequence_length * hidden_size * bytes_per_param * layers
    estimated_per_sample = (sequence_length * 4096 * 2 * 32) / 1024**3  # 粗略估算
    
    if estimated_per_sample > 0:
        batch_size = max(1, int(available_memory / estimated_per_sample))
    else:
        batch_size = 1
    
    logger.info(f"GPU内存优化建议: 总内存={total_memory:.1f}GB, "
               f"可用={available_memory:.1f}GB, 推荐batch_size={batch_size}")
    
    return batch_size

def monitor_gpu_usage():
    """监控GPU使用情况"""
    if not torch.cuda.is_available():
        return
    
    print("\n📊 GPU使用情况:")
    for i in range(torch.cuda.device_count()):
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        total = torch.cuda.get_device_properties(i).total_memory / 1024**3
        
        usage_percent = (allocated / total) * 100
        print(f"GPU {i}: {allocated:.2f}/{total:.1f} GB ({usage_percent:.1f}%)")

def clear_gpu_cache():
    """清空GPU缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("🔧 GPU缓存已清空")

def get_device_map(num_gpus: Optional[int] = None):
    """
    获取适合多GPU的设备映射
    
    Args:
        num_gpus: 使用的GPU数量，None表示使用所有可用GPU
    
    Returns:
        设备映射配置
    """
    if not torch.cuda.is_available():
        return "cpu"
    
    if num_gpus is None:
        num_gpus = torch.cuda.device_count()
    
    if num_gpus == 1:
        return "auto"  # 单GPU自动分配
    else:
        return "auto"  # 多GPU自动分配，让transformers处理
