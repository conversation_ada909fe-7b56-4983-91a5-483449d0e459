"""
本地数据集处理器 - 从本地CSV/JSON文件加载数据集
"""

import json
import pandas as pd
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

# 导入基类
# from .datasets import DatasetProcessor
# from .mmlu_few_shot_examples import get_mmlu_few_shot_examples
# from .mmlu_cot_few_shot_examples import get_mmlu_cot_few_shot_examples

from datasets import DatasetProcessor
from mmlu_few_shot_examples import get_mmlu_few_shot_examples
from mmlu_cot_few_shot_examples import get_mmlu_cot_few_shot_examples

logger = logging.getLogger(__name__)

class LocalDatasetProcessor(DatasetProcessor):
    """本地数据集处理基类"""
    
    def __init__(self, dataset_name: str, dataset_dir: str = "/home/<USER>/Documentation/ICLR26/CISC_v/dataset"):
        self.dataset_name = dataset_name
        self.dataset_dir = Path(dataset_dir)
        self.dataset = None
        self.load_dataset()
    
    def load_dataset(self):
        """加载本地数据集"""
        raise NotImplementedError
    
    ## NEW ##
    def format_confidence_prompt(self, example: Dict[str, Any], generated_answer: str) -> str:
        """为获取置信度分数格式化prompt"""
        raise NotImplementedError
    
    ## NEW2 ##
    def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
        """为获取推理路径置信度分数格式化prompt"""
        raise NotImplementedError


class LocalMMLUProcessor(LocalDatasetProcessor):
    """本地MMLU数据集处理器"""
    
    def __init__(self):
        super().__init__("MMLU")
    
    def load_dataset(self):
        """从本地CSV文件加载MMLU数据集"""
        try:
            # 加载测试集
            test_path = self.dataset_dir / "mmlu" / "mmlu_test.csv"
            if test_path.exists():
                df = pd.read_csv(test_path)
                self.dataset = df.to_dict('records')
                logger.info(f"从本地加载MMLU数据集: {len(self.dataset)} 个样本")
            else:
                raise FileNotFoundError(f"本地MMLU数据集文件不存在: {test_path}")
        except Exception as e:
            logger.error(f"加载本地MMLU数据集失败: {e}")
            raise
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None, use_cot: bool = False, prompt_template: str = "default") -> str:
    ###CoT implement
        """格式化MMLU的prompt，支持few-shot和CoT示例"""
        
        prompt = ""
        
        # 添加few-shot示例
        if few_shot_examples:
            for shot_example in few_shot_examples:
                # CoT示例已经预先格式化好了
                if use_cot and "formatted_prompt" in shot_example:
                    prompt += shot_example["formatted_prompt"] + "\n\n"
                else: # 处理标准few-shot示例
                    shot_question = shot_example["question"]
                    shot_choices = shot_example["choices"]
                    shot_answer = shot_example.get("answer", shot_example.get("correct_answer", ""))
                    
                    if isinstance(shot_choices, str):
                        try: shot_choices = eval(shot_choices)
                        except: shot_choices = shot_choices.split('\n')
                    
                    shot_options_str = ""
                    for i, choice in enumerate(shot_choices):
                        shot_options_str += f"({chr(65 + i)}) {choice}\n"
                    
                    if str(shot_answer).isdigit(): shot_answer_letter = chr(65 + int(shot_answer))
                    else: shot_answer_letter = str(shot_answer).upper()
                    
                    prompt += f"""Question: {shot_question}\n\n{shot_options_str}Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, or D).\n\nAnswer: {shot_answer_letter}\n\n"""
        
        # 添加当前问题
        question = example["question"]
        choices = example["choices"]
        
        if isinstance(choices, str):
            try: choices = eval(choices)
            except: choices = choices.split('\n')
        
        options_str = ""
        for i, choice in enumerate(choices):
            options_str += f"({chr(65 + i)}) {choice}\n"
        
        # 根据是否使用CoT选择不同的指令
        if use_cot:
            instruction = "First, provide a step-by-step reasoning for your answer. After your reasoning, conclude with the final answer in the format 'The final answer is (X)'."
        else:
            instruction = "Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, or D)."
            
        prompt += f"""Question: {question}\n\n{options_str}{instruction}\n\nAnswer:"""
        
        return prompt
    

    ###CoT implementation
    def get_few_shot_examples(self, n_shots: int, exclude_indices: Optional[set] = None, use_cot: bool = False, prompt_template: str = "default") -> List[Dict[str, Any]]:
        """获取few-shot示例，支持标准和CoT两种模式"""
        if n_shots <= 0:
            return []

        if use_cot:
            # 使用包含推理过程的CoT 示例
            return get_mmlu_cot_few_shot_examples(n_shots)
        else:
            # 使用标准示例
            return get_mmlu_few_shot_examples(n_shots)




    ### CoT implementation
    def extract_answer(self, response: str, use_cot: bool = False) -> str:
        """从响应中提取答案选项，支持CoT"""
        options_range = "A-D"
        if use_cot:
            # 策略1: 查找 "The final answer is (X)"
            match = re.search(rf"The final answer is \(([{options_range}])\)", response, re.IGNORECASE)
            if match:
                return match.group(1).upper()
            
            # 策略2: (Fallback) 查找括号中的选项，通常在末尾
            matches = re.findall(rf'\(([{options_range}])\)', response.upper())
            if matches:
                return matches[-1]
                
            # 策略3: (Fallback) 查找独立的字母选项
            matches = re.findall(rf'\b([{options_range}])\b', response.upper())
            if matches:
                return matches[-1]
            return ""
        else:
            # 原始的非CoT提取逻辑
            pattern = rf'\b([{options_range}])\b'
            matches = re.findall(pattern, response.upper())
            if matches: return matches[0]
            
            pattern = rf'\(([{options_range}])\)'
            matches = re.findall(pattern, response.upper())
            if matches: return matches[0]
            
            return ""
        



    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查答案是否正确"""
        # MMLU的答案可能是数字格式(0,1,2,3)或字母格式
        if str(gold_answer).isdigit():
            # 数字格式，转换为字母
            gold_letter = chr(65 + int(gold_answer))  # 0->A, 1->B, 2->C, 3->D
        else:
            # 已经是字母格式
            gold_letter = str(gold_answer).upper()
        
        return pred_answer.upper() == gold_letter
    


    ## NEW ##
    def format_confidence_prompt(self, example: Dict[str, Any], generated_answer: str) -> str:
        """为MMLU格式化置信度prompt"""
        question = self.format_prompt(example, use_cot=False) # 复用原来的prompt格式
        # 移除结尾的 "Answer:"
        question = question.rsplit("Answer:", 1)[0].strip()
        
        return f"""The following is a multiple-choice question and a proposed answer.
            Question:
            {question}

            Proposed Answer: {generated_answer}

            Is the proposed answer correct? Please answer with only "T" or "F".
            Answer:"""

    def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
        """为MMLU格式化推理路径置信度prompt"""
        question_prompt = self.format_prompt(example, use_cot=False)
        question_prompt = question_prompt.rsplit("Answer:", 1)[0].strip()

        return f"""The following is a multiple-choice question and a proposed reasoning path to solve it.

            Question:
            {question_prompt}

            Proposed Reasoning Path:
            {reasoning_path}

            Is the reasoning process logical and correct, leading towards a valid conclusion? Please answer with only "T" or "F".
            Answer:"""




class LocalMMLUProProcessor(LocalDatasetProcessor):
    """本地MMLU-Pro数据集处理器(已完成CoT实现)"""
    
    def __init__(self):
        super().__init__("MMLU-Pro")
    
    def load_dataset(self):
        """从本地CSV文件加载MMLU-Pro数据集"""
        try:
            test_path = self.dataset_dir / "mmlu-pro" / "mmlu_pro_test.csv"
            if test_path.exists():
                df = pd.read_csv(test_path)
                self.dataset = df.to_dict('records')
                logger.info(f"从本地加载MMLU-Pro数据集: {len(self.dataset)} 个样本")
            else:
                raise FileNotFoundError(f"本地MMLU-Pro数据集文件不存在: {test_path}")
        except Exception as e:
            logger.error(f"加载本地MMLU-Pro数据集失败: {e}")
            raise
            
    # --- START OF CORRECTED AND COMPLETED CODE ---
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None, use_cot: bool = False, prompt_template: str = "default") -> str:
        """格式化MMLU-Pro的prompt，支持few-shot和CoT"""
        prompt = ""

        # 1. 添加few-shot示例 (逻辑与MMLU一致)
        if few_shot_examples:
            for shot_example in few_shot_examples:
                if use_cot and "formatted_prompt" in shot_example:
                    prompt += shot_example["formatted_prompt"] + "\n\n"
                else: # 标准few-shot
                    shot_question = shot_example["question"]
                    shot_choices = shot_example["choices"]
                    shot_answer = shot_example.get("answer", shot_example.get("correct_answer", ""))
                    
                    if isinstance(shot_choices, str):
                        try: shot_choices = eval(shot_choices)
                        except: shot_choices = shot_choices.split('\n')
                    
                    shot_options_str = ""
                    for i, choice in enumerate(shot_choices):
                        shot_options_str += f"({chr(65 + i)}) {choice}\n"
                    
                    if str(shot_answer).isdigit(): shot_answer_letter = chr(65 + int(shot_answer))
                    else: shot_answer_letter = str(shot_answer).upper()
                    
                    # 注意：指令中的选项范围是动态的，但此处为了示例统一性，使用通用文本
                    prompt += f"Question: {shot_question}\n\n{shot_options_str}Please select the correct answer from the options above.\n\nAnswer: {shot_answer_letter}\n\n"

        # 2. 添加当前问题
        question = example["question"]
        options = example["options"]
        
        if isinstance(options, str):
            try: options = eval(options)
            except: options = [opt.strip() for opt in options.split('\n') if opt.strip()]

        options_str = ""
        for i, option in enumerate(options):
            options_str += f"({chr(65 + i)}) {option}\n"
        
        # 3. 根据是否使用CoT选择指令
        if use_cot:
            instruction = "First, provide a step-by-step reasoning for your answer. After your reasoning, conclude with the final answer in the format 'The final answer is (X)'."
        else:
            instruction = "Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, D, E, F, G, H, I, or J)."

        prompt += f"Question: {question}\n\n{options_str}{instruction}\n\nAnswer:"
        return prompt


    def get_few_shot_examples(self, n_shots: int, exclude_indices: Optional[set] = None, use_cot: bool = False, prompt_template: str = "default") -> List[Dict[str, Any]]:
        """
        获取MMLU-Pro的few-shot示例。
        目前实现为返回空列表，因为MMLU-Pro通常以Zero-Shot方式评估。
        """
        # if n_shots > 0:
        #     logger.warning("MMLU-Pro处理器当前不提供few-shot示例，将以Zero-Shot模式运行。")
        # return []
        """获取few-shot示例，复用MMLU的示例"""
        if n_shots <= 0:
            return []
        
        # MMLU-Pro 可以复用 MMLU 的 CoT 示例
        if use_cot:
            return get_mmlu_cot_few_shot_examples(n_shots)
        else:
            # 对于标准 few-shot，也可以复用
            return get_mmlu_few_shot_examples(n_shots)

    def extract_answer(self, response: str, use_cot: bool = False) -> str:
        """从响应中提取答案选项，支持CoT"""
        options_range = "A-J"  # MMLU-Pro的选项范围

        if use_cot:
            # 策略1: 查找 "The final answer is (X)"
            match = re.search(rf"The final answer is \(([{options_range}])\)", response, re.IGNORECASE)
            if match:
                return match.group(1).upper()
            
            # 策略2: 查找响应末尾的 (X)
            matches = re.findall(rf"\(([{options_range}])\)", response.upper())
            if matches:
                return matches[-1]
            
            # 策略3: 查找最后一个独立的字母
            matches = re.findall(rf"\b([{options_range}])\b", response.upper())
            if matches:
                return matches[-1]
            
            return ""  # 如果都找不到，返回空
        else:
            # 原始的非CoT提取逻辑
            pattern = rf'\b([{options_range}])\b'
            matches = re.findall(pattern, response.upper())
            if matches: return matches[0]
            
            pattern = rf'\(([{options_range}])\)'
            matches = re.findall(pattern, response.upper())
            if matches: return matches[0]
            
            return ""

    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查答案是否正确"""
        # MMLU-Pro的gold_answer通常直接是字母
        return pred_answer.upper() == str(gold_answer).upper()

    def format_confidence_prompt(self, example: Dict[str, Any], generated_answer: str) -> str:
        """为MMLU-Pro格式化置信度prompt"""
        # 使用非CoT模式生成基础问题部分，以保持置信度判断的一致性
        question_prompt = self.format_prompt(example, use_cot=False)
        question_prompt = question_prompt.rsplit("Answer:", 1)[0].strip()
        
        return f"""The following is a multiple-choice question and a proposed answer.
Question:
{question_prompt}

Proposed Answer: {generated_answer}

Is the proposed answer correct? Please answer with only "T" or "F".
Answer:"""
    
    
    ## NEW ##
    def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
        """为MMLU-Pro格式化推理路径置信度prompt"""
        question_prompt = self.format_prompt(example, use_cot=False)
        question_prompt = question_prompt.rsplit("Answer:", 1)[0].strip()

        return f"""The following is a multiple-choice question and a proposed reasoning path to solve it.

Question:
{question_prompt}

Proposed Reasoning Path:
{reasoning_path}

Is the reasoning process logical and correct, leading towards a valid conclusion? Please answer with only "T" or "F".
Answer:"""
    




class LocalGSM8KProcessor(LocalDatasetProcessor):
    """本地GSM8K数据集处理器"""
    
    def __init__(self):
        self.train_dataset = None
        self.cot_examples = None
        self.prompt_templates = {}
        self.specialized_examples = {}
        super().__init__("GSM8K")
        self.load_prompt_templates()
    
    def load_dataset(self):
        """从本地文件加载GSM8K数据集"""
        try:
            # 加载测试集
            test_json_path = self.dataset_dir / "gsm8k" / "gsm8k_test.json"
            if test_json_path.exists():
                with open(test_json_path, 'r', encoding='utf-8') as f:
                    self.dataset = json.load(f)
                logger.info(f"从本地加载GSM8K测试集: {len(self.dataset)} 个样本")
            else:
                # 尝试CSV文件
                test_path = self.dataset_dir / "gsm8k" / "gsm8k_test.csv"
                if test_path.exists():
                    df = pd.read_csv(test_path)
                    self.dataset = df.to_dict('records')
                    logger.info(f"从本地加载GSM8K数据集: {len(self.dataset)} 个样本")
                else:
                    raise FileNotFoundError(f"本地GSM8K数据集文件不存在: {test_json_path} 或 {test_path}")
            
            # 加载训练集用于few-shot示例
            train_json_path = self.dataset_dir / "gsm8k" / "gsm8k_train.json"
            if train_json_path.exists():
                with open(train_json_path, 'r', encoding='utf-8') as f:
                    self.train_dataset = json.load(f)
                logger.info(f"从本地加载GSM8K训练集: {len(self.train_dataset)} 个样本")
            
            # 加载CoT示例
            cot_path = self.dataset_dir / "gsm8k" / "cot.json"
            if cot_path.exists():
                with open(cot_path, 'r', encoding='utf-8') as f:
                    cot_data = json.load(f)
                    self.cot_examples = cot_data.get("cot_pool", [])
                logger.info(f"从本地加载GSM8K CoT示例: {len(self.cot_examples)} 个")
                
        except Exception as e:
            logger.error(f"加载本地GSM8K数据集失败: {e}")
            raise

    def load_prompt_templates(self):
        """加载prompt模板配置"""
        try:
            template_path = Path("gsm8k_prompt_templates.json")
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                    self.prompt_templates = template_data.get("prompt_templates", {})
                    self.specialized_examples = template_data.get("specialized_few_shot_examples", {})
                logger.info(f"加载GSM8K prompt模板: {list(self.prompt_templates.keys())}")
            else:
                logger.warning("GSM8K prompt模板文件不存在，使用默认模板")
                self._load_default_templates()
        except Exception as e:
            logger.error(f"加载prompt模板失败: {e}")
            self._load_default_templates()
    
    def _load_default_templates(self):
        """加载默认模板"""
        self.prompt_templates = {
            "intuitionist": {
                "name": "The Intuitionist (Zero-shot CoT)",
                "user_template": "Q: {question}\nA: Please answer this question by first reasoning step by step and then providing your answer.",
                "use_few_shot": False,
                "cot_style": "intuitive"
            }
        }
        self.specialized_examples = {}

    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[str]] = None, use_cot: bool = True, prompt_template: str = "intuitionist") -> str:
        """格式化GSM8K的prompt，支持多种模板"""
        question = example["question"]
        
        # 获取指定的模板
        template_config = self.prompt_templates.get(prompt_template, self.prompt_templates.get("intuitionist", {}))
        
        if not template_config:
            # 回退到默认格式
            if few_shot_examples:
                few_shot_text = "\n".join(few_shot_examples)
                return f"{few_shot_text}Q: {question}\nA:"
            else:
                return f"Q: {question}\nA:"
        
        # 使用模板格式化
        user_template = template_config.get("user_template", "Q: {question}\nA:")
        use_template_few_shot = template_config.get("use_few_shot", True)
        
        if use_template_few_shot and few_shot_examples:
            # 使用few-shot示例
            few_shot_prefix = template_config.get("few_shot_prefix", "")
            few_shot_suffix = template_config.get("few_shot_suffix", user_template)
            
            few_shot_text = "\n".join(few_shot_examples)
            if few_shot_prefix:
                prompt = f"{few_shot_prefix}{few_shot_text}{few_shot_suffix.format(question=question)}"
            else:
                prompt = f"{few_shot_text}{few_shot_suffix.format(question=question)}"
        else:
            # 零样本或不使用few-shot的模板
            prompt = user_template.format(question=question)
        
        return prompt
    
    def get_few_shot_examples(self, num_examples: int, exclude_indices: Optional[set] = None, use_cot: bool = True, prompt_template: str = "intuitionist") -> List[str]:
        """获取few-shot示例，支持不同的prompt模板"""
        if num_examples <= 0:
            return []
        
        import random
        # 设置随机种子以确保可复现性
        random.seed(42)  # 添加这行
    
        # 检查是否有专门的模板示例
        if prompt_template in self.specialized_examples:
            specialized_examples = self.specialized_examples[prompt_template]
            if len(specialized_examples) >= num_examples:
                # 使用专门的模板示例
                import random
                return random.sample(specialized_examples, num_examples)
        
        # 优先使用预定义的CoT示例
        if self.cot_examples and len(self.cot_examples) >= num_examples:
            # 从CoT示例中随机选择
            import random
            selected_examples = random.sample(self.cot_examples, num_examples)
            return selected_examples
        
        # 如果CoT示例不够，从训练集中生成
        if self.train_dataset is None:
            logger.warning("训练数据集未加载，无法提供few-shot示例")
            return []
        
        exclude_indices = exclude_indices or set()
        available_indices = [i for i in range(len(self.train_dataset)) if i not in exclude_indices]
        
        if len(available_indices) < num_examples:
            logger.warning(f"可用训练样本不足: 需要 {num_examples}, 可用 {len(available_indices)}")
            num_examples = len(available_indices)
        
        # 随机选择训练样本
        import random
        selected_indices = random.sample(available_indices, num_examples)
        
        examples = []
        for idx in selected_indices:
            train_example = self.train_dataset[idx]
            question = train_example["question"]
            answer = train_example["answer"]
            
            # 从答案中提取最终数值
            final_answer = self.extract_answer(answer)
            
            # 格式化为CoT风格
            example_text = f"Q: {question}\nA: {answer.split('####')[0].strip()} The answer is {final_answer}.\n"
            examples.append(example_text)
        
        return examples

    def extract_answer(self, response: str, use_cot: bool = True) -> str:
        """从响应中提取数值答案"""
        # 首先尝试查找 "The answer is X" 格式
        answer_pattern = r'The answer is\s+([+-]?\d+(?:\.\d+)?)'
        answer_match = re.search(answer_pattern, response, re.IGNORECASE)
        if answer_match:
            return answer_match.group(1).replace(',', '')
        
        # 然后尝试查找 "#### X" 格式（GSM8K标准格式）
        gsm8k_pattern = r'####\s*([+-]?\d+(?:\.\d+)?)'
        gsm8k_match = re.search(gsm8k_pattern, response)
        if gsm8k_match:
            return gsm8k_match.group(1).replace(',', '')
        
        # 最后查找所有数字，返回最后一个
        numbers = re.findall(r'[+-]?\d+(?:\.\d+)?', response)
        
        if numbers:
            # 返回最后一个数字
            return numbers[-1].replace(',', '')
        
        return ""
    
    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查数值答案是否正确"""
        try:
            # 从gold_answer中提取数值（GSM8K的答案格式为"#### 数值"）
            gold_nums = re.findall(r'-?\d+(?:\.\d+)?', gold_answer)
            if not gold_nums:
                return False
            
            gold_val = float(gold_nums[-1].replace(',', ''))
            pred_val = float(pred_answer.replace(',', ''))
            
            # 允许小的浮点误差
            return abs(gold_val - pred_val) < 1e-6
            
        except (ValueError, TypeError):
            return False
    
    def get_available_templates(self) -> Dict[str, str]:
        """获取可用的prompt模板"""
        return {template_id: config.get("name", template_id) 
                for template_id, config in self.prompt_templates.items()}
    
    def get_template_description(self, template_id: str) -> str:
        """获取模板描述"""
        template_config = self.prompt_templates.get(template_id, {})
        return template_config.get("description", "无描述")
        

    ## NEW ##
    def format_confidence_prompt(self, example: Dict[str, Any], generated_answer: str) -> str:
        """为GSM8K格式化置信度prompt"""
        question = example["question"]
        
        return f"""The following is a math problem and a proposed solution.
            Problem: {question}

            Proposed Solution and Answer:
            {generated_answer}

            Based on the final numerical result in the proposed solution, is the solution correct? Please answer with only "T" or "F".
            Answer:"""
        ## NEW ##

    def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
        """为GSM8K格式化推理路径置信度prompt"""
        question = example["question"]
        return f"""The following is a math problem and a proposed reasoning path to solve it.

            Problem:
            {question}

            Proposed Reasoning Path:
            {reasoning_path}

            Is the reasoning process logical, mathematically sound, and correct? Please answer with only "T" or "F".
            Answer:"""

    # def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
    #     """为GSM8K格式化推理路径置信度prompt"""
    #     question = example["question"]
    #     return f"""The following is a math problem and a proposed reasoning path to solve it.

    #         Problem:
    #         {question}

    #         Proposed Reasoning Path:
    #         {reasoning_path}

    #         Is the reasoning process logical, mathematically sound, and correct? Please answer with only "T" or "F".
    #         Answer:"""


class LocalHumanEvalProcessor(LocalDatasetProcessor):
    """HumanEval代码生成数据集处理器 - 支持CoT和Self-Consistency"""
    
    def __init__(self):
        super().__init__("HumanEval")
        self.few_shot_config = None
    
    def load_dataset(self):
        """加载HumanEval数据集"""
        try:
            # 首先尝试从HuggingFace加载
            from datasets import load_dataset
            ds = load_dataset("openai_humaneval", split="test")
            self.dataset = []
            
            for item in ds:
                self.dataset.append({
                    "task_id": item["task_id"],
                    "prompt": item["prompt"],
                    "canonical_solution": item["canonical_solution"],
                    "test": item["test"],
                    "entry_point": item["entry_point"]
                })
            
            logger.info(f"从HuggingFace加载HumanEval数据集: {len(self.dataset)} 个样本")
            
        except Exception as e:
            logger.error(f"加载HumanEval数据集失败: {e}")
            # 使用预定义的测试样本
            self.dataset = [
                {
                    "task_id": "HumanEval/0",
                    "prompt": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n",
                    "canonical_solution": "    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if abs(numbers[i] - numbers[j]) < threshold:\n                return True\n    return False\n",
                    "test": "def check(candidate):\n    assert candidate([1.0, 2.0, 3.0], 0.5) == False\n    assert candidate([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3) == True",
                    "entry_point": "has_close_elements"
                }
            ]
            logger.warning("使用预定义测试数据集")
    
    def load_few_shot_config(self):
        """加载few-shot配置"""
        try:
            config_path = Path(__file__).parent.parent / "configs" / "humaneval_few_shot_examples.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.few_shot_config = json.load(f)
                logger.info("已加载HumanEval few-shot配置")
            else:
                logger.warning("未找到HumanEval few-shot配置文件")
        except Exception as e:
            logger.error(f"加载HumanEval few-shot配置失败: {e}")
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[str]] = None, use_cot: bool = True, prompt_template: str = "default") -> str:
        """格式化提示词"""
        if self.few_shot_config is None:
            self.load_few_shot_config()
        
        # 获取问题描述
        problem = example["prompt"]
        
        # 构建few-shot示例
        few_shot_text = ""
        if few_shot_examples and len(few_shot_examples) > 0:
            few_shot_text = "\n\n".join(few_shot_examples)
        
        # 根据CoT模式选择模板
        if use_cot:
            # CoT模式：要求逐步推理
            if few_shot_text:
                prompt = f"""你是一个专业的Python程序员。请仔细分析问题，使用逐步推理的方法完成代码。

以下是一些示例：

{few_shot_text}

现在请完成以下Python函数：

{problem}

请按照以下步骤：
1. 理解函数要求和预期行为
2. 分析输入输出的关系和边界情况
3. 设计算法或解决方案
4. 实现完整的代码

请提供你的分析过程和最终代码。"""
            else:
                prompt = f"""你是一个专业的Python程序员。请仔细分析问题，使用逐步推理的方法完成代码。

请完成以下Python函数：

{problem}

请按照以下步骤：
1. 理解函数要求和预期行为
2. 分析输入输出的关系和边界情况
3. 设计算法或解决方案
4. 实现完整的代码

请提供你的分析过程和最终代码。"""
        else:
            # 直接模式：直接要求代码
            if few_shot_text:
                prompt = f"""你是一个专业的Python程序员。请直接完成给定的函数。

以下是一些示例：

{few_shot_text}

现在请完成以下Python函数：

{problem}"""
            else:
                prompt = f"""你是一个专业的Python程序员。请直接完成给定的函数。

请完成以下Python函数：

{problem}"""
        
        return prompt
    
    def extract_answer(self, response: str, use_cot: bool = False) -> str:
        """从响应中提取Python代码"""
        import re
        
        # 1. 优先提取```python代码块
        python_code_pattern = r'```python\s*(.*?)\s*```'
        matches = re.findall(python_code_pattern, response, re.DOTALL)
        if matches:
            return self._clean_code(matches[-1])
        
        # 2. 提取一般```代码块
        code_block_pattern = r'```\s*(.*?)\s*```'
        matches = re.findall(code_block_pattern, response, re.DOTALL)
        if matches:
            return self._clean_code(matches[-1])
        
        # 3. 提取def开头的函数定义
        def_pattern = r'(def\s+\w+.*?)(?=\n\s*(?:def|class|$|分析|步骤|总结))'
        matches = re.findall(def_pattern, response, re.DOTALL | re.MULTILINE)
        if matches:
            # 选择最长的匹配（通常最完整）
            longest_match = max(matches, key=len)
            return self._clean_code(longest_match)
        
        # 4. 宽松模式：查找任何包含def的行到下一个空行
        lines = response.split('\n')
        code_lines = []
        in_function = False
        
        for line in lines:
            if line.strip().startswith('def '):
                in_function = True
                code_lines = [line]
            elif in_function:
                if line.strip() == '' and len(code_lines) > 1:
                    # 遇到空行且已有内容，结束提取
                    break
                elif line.strip().startswith(('分析', '步骤', '总结', '解释', '注意')):
                    # 遇到解释性文字，结束提取
                    break
                else:
                    code_lines.append(line)
        
        if code_lines:
            return self._clean_code('\n'.join(code_lines))
        
        # 5. 如果都失败，返回原始响应（可能有些模型直接返回代码）
        return response.strip()
    
    def _clean_code(self, code: str) -> str:
        """清理提取的代码"""
        # 去除首尾空白
        code = code.strip()
        
        # 去除可能的语言标识符
        if code.startswith('python'):
            code = code[6:].strip()
        
        # 去除可能的额外标记
        for marker in ['最终代码：', '代码：', '实现：', '解决方案：']:
            if marker in code:
                code = code.split(marker)[-1].strip()
        
        return code
    
    def check_answer(self, predicted: str, ground_truth: str) -> bool:
        """检查代码答案的基本正确性"""
        try:
            # 检查语法正确性
            compile(predicted, '<string>', 'exec')
            
            # 检查是否包含函数定义
            if 'def ' not in predicted:
                return False
            
            return True
            
        except SyntaxError:
            return False
        except Exception:
            return False
    
    def get_few_shot_examples(self, num_examples: int = 3, exclude_indices: Optional[set] = None, use_cot: bool = True, prompt_template: str = "default") -> List[str]:
        """获取few-shot示例"""
        if self.few_shot_config is None:
            self.load_few_shot_config()
        
        examples = []
        
        if self.few_shot_config and "humaneval_few_shot_examples" in self.few_shot_config:
            humaneval_examples = self.few_shot_config["humaneval_few_shot_examples"]
            
            for i, example in enumerate(humaneval_examples[:num_examples]):
                if exclude_indices and i in exclude_indices:
                    continue
                
                problem = example["problem"]
                
                if use_cot:
                    # 使用CoT示例
                    solution = example.get("solution_with_cot", example.get("solution_direct", ""))
                else:
                    # 使用直接示例
                    solution = example.get("solution_direct", example.get("solution_with_cot", ""))
                
                example_text = f"""问题：
{problem}

解答：
{solution}"""
                
                examples.append(example_text)
        
        # 如果配置文件不可用，使用内置示例
        if not examples:
            examples = self._get_builtin_examples(num_examples, use_cot)
        
        return examples[:num_examples]
    
    def _get_builtin_examples(self, num_examples: int, use_cot: bool) -> List[str]:
        """获取内置示例"""
        if use_cot:
            examples = [
                """问题：
def has_close_elements(numbers: List[float], threshold: float) -> bool:
    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than
    given threshold.
    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)
    False
    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)
    True
    \"\"\"

解答：
让我逐步分析这个问题：
1. 需要检查列表中任意两个数字是否距离小于阈值
2. 可以用双重循环比较所有数字对
3. 如果找到任何一对距离小于阈值的数字，返回True
4. 如果所有数字对都不满足条件，返回False

```python
def has_close_elements(numbers: List[float], threshold: float) -> bool:
    for i in range(len(numbers)):
        for j in range(i + 1, len(numbers)):
            if abs(numbers[i] - numbers[j]) < threshold:
                return True
    return False
```""",
                
                """问题：
def separate_paren_groups(paren_string: str) -> List[str]:
    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to
    separate those group and return the list of those.
    Separate groups are balanced (each open brace is properly closed) and not nested within each other
    Ignore any spaces in the input string.
    >>> separate_paren_groups('( ) (( )) (( )( ))')
    ['()', '(())', '(()())']
    \"\"\"

解答：
让我逐步分析这个问题：
1. 需要将字符串中的多个括号组分离
2. 每个组都是平衡的括号
3. 需要忽略空格
4. 可以用计数器跟踪括号平衡状态

```python
def separate_paren_groups(paren_string: str) -> List[str]:
    result = []
    current_group = ""
    balance = 0
    
    for char in paren_string:
        if char == ' ':
            continue
        
        current_group += char
        
        if char == '(':
            balance += 1
        elif char == ')':
            balance -= 1
        
        if balance == 0:
            result.append(current_group)
            current_group = ""
    
    return result
```"""
            ]
        else:
            examples = [
                """问题：
def has_close_elements(numbers: List[float], threshold: float) -> bool:
    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than
    given threshold.
    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)
    False
    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)
    True
    \"\"\"

解答：
```python
def has_close_elements(numbers: List[float], threshold: float) -> bool:
    for i in range(len(numbers)):
        for j in range(i + 1, len(numbers)):
            if abs(numbers[i] - numbers[j]) < threshold:
                return True
    return False
```""",
                
                """问题：
def separate_paren_groups(paren_string: str) -> List[str]:
    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to
    separate those group and return the list of those.
    Separate groups are balanced (each open brace is properly closed) and not nested within each other
    Ignore any spaces in the input string.
    >>> separate_paren_groups('( ) (( )) (( )( ))')
    ['()', '(())', '(()())']
    \"\"\"

解答：
```python
def separate_paren_groups(paren_string: str) -> List[str]:
    result = []
    current_group = ""
    balance = 0
    
    for char in paren_string:
        if char == ' ':
            continue
        
        current_group += char
        
        if char == '(':
            balance += 1
        elif char == ')':
            balance -= 1
        
        if balance == 0:
            result.append(current_group)
            current_group = ""
    
    return result
```"""
            ]
        
        return examples
    
    def format_confidence_prompt(self, example: Dict[str, Any], generated_answer: str) -> str:
        """格式化置信度评估提示词"""
        problem = example["prompt"]
        return f"""请评估以下代码解决方案的正确性。

任务：完成以下Python函数
{problem}

生成的解决方案：
{generated_answer}

请考虑以下方面：
1. 算法的正确性
2. 边界情况的处理
3. 代码质量和效率
4. 是否符合函数规范

请提供你对此解决方案正确性的置信度，用0到1之间的数字表示：
- 0表示完全不确定/错误
- 1表示完全确定/正确

置信度："""
    
    def format_path_confidence_prompt(self, example: Dict[str, Any], reasoning_path: str) -> str:
        """格式化推理路径置信度评估提示词"""
        problem = example["prompt"]
        return f"""请评估以下推理过程的质量。

任务：完成以下Python函数
{problem}

推理过程：
{reasoning_path}

请考虑以下方面：
1. 推理的逻辑性
2. 算法方法的正确性
3. 边界情况的考虑
4. 分析的清晰度和完整性

请提供你对此推理过程质量的置信度，用0到1之间的数字表示：
- 0表示推理完全错误
- 1表示推理完全正确

置信度："""


def get_local_dataset_processor(dataset_name: str) -> LocalDatasetProcessor:
    """获取本地数据集处理器"""
    if dataset_name.lower() == "mmlu":
        return LocalMMLUProcessor()
    elif dataset_name.lower() == "mmlu-pro":
        return LocalMMLUProProcessor()
    elif dataset_name.lower() == "gsm8k":
        return LocalGSM8KProcessor()
    elif dataset_name.lower() in ["humaneval", "human_eval", "human-eval"]:
        return LocalHumanEvalProcessor()
    else:
        raise ValueError(f"Unsupported local dataset: {dataset_name}")

def sample_local_dataset(processor: LocalDatasetProcessor, num_samples: Optional[int] = None) -> List[Dict[str, Any]]:
    """采样本地数据集"""
    if processor.dataset is None:
        raise ValueError("Dataset is not loaded")
    
    if num_samples is None or num_samples >= len(processor.dataset):
        return processor.dataset
    
    # 随机采样
    import random
    indices = random.sample(range(len(processor.dataset)), num_samples)
    return [processor.dataset[i] for i in indices]
