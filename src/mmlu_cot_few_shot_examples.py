"""
MMLU数据集的Chain-of-Thought (CoT) few-shot examples
包含问题、选项、推理过程和最终答案。
"""
MMLU_COT_FEW_SHOT_EXAMPLES = [
    {
        'question': 'Which of the following cases established the precedent that a defendant must be informed of the right to remain silent, the right to a lawyer, and protection from self-incrimination?',
        'subject': 'high_school_government_and_politics',
        'choices': [
            'Weeks v. United States',
            'Betts v. Brady',
            'Mapp v. Ohio',
            'Miranda v. Arizona'
        ],
        'answer': 3,
        'reasoning': "Let's analyze the options. '<PERSON> v. United States' is associated with the exclusionary rule at the federal level. 'Mapp v. Ohio' extended the exclusionary rule to the states. '<PERSON>ts v. Brady' dealt with the right to counsel, but was later overturned. 'Miranda v. Arizona' is the landmark case that established the requirement for police to inform suspects of their rights before questioning, now known as Miranda rights. These rights include the right to remain silent and the right to an attorney. Therefore, Miranda v. Arizona is the correct case.",
        'formatted_prompt': """Question: Which of the following cases established the precedent that a defendant must be informed of the right to remain silent, the right to a lawyer, and protection from self-incrimination?

(A) Weeks v. United States
(B) Betts v. Brady
(C) Mapp v. Ohio
(D) Miranda v. Arizona
First, provide a step-by-step reasoning for your answer. After your reasoning, conclude with the final answer in the format 'The final answer is (X)'.

Answer: Let's analyze the options. 'Weeks v. United States' is associated with the exclusionary rule at the federal level. 'Mapp v. Ohio' extended the exclusionary rule to the states. 'Betts v. Brady' dealt with the right to counsel, but was later overturned. 'Miranda v. Arizona' is the landmark case that established the requirement for police to inform suspects of their rights before questioning, now known as Miranda rights. These rights include the right to remain silent and the right to an attorney. Therefore, Miranda v. Arizona is the correct case. The final answer is (D)"""
    },
    {
        'question': 'A new compound is synthesized and found to be a monoprotic acid with a molar mass of 248 g/mol. When 0.0050 mol of this acid are dissolved in 0.500 L of water, the pH is measured as 3.89. What is the pKa of this acid?',
        'subject': 'high_school_chemistry',
        'choices': [
            '3.89',
            '7.78',
            '5.78',
            '2.33'
        ],
        'answer': 2,
        'reasoning': "1. First, calculate the initial concentration of the acid (HA). Concentration = moles / volume = 0.0050 mol / 0.500 L = 0.010 M.\n2. Next, calculate the concentration of H+ ions from the pH. [H+] = 10^(-pH) = 10^(-3.89) = 1.288 x 10^-4 M.\n3. For a monoprotic acid, the dissociation is HA <=> H+ + A-. At equilibrium, [H+] = [A-] = 1.288 x 10^-4 M, and [HA] = 0.010 - 1.288 x 10^-4 ≈ 0.010 M (since the dissociation is small).\n4. Now, calculate the acid dissociation constant, Ka. Ka = ([H+][A-]) / [HA] = (1.288 x 10^-4)^2 / 0.010 = 1.659 x 10^-6.\n5. Finally, calculate the pKa. pKa = -log10(Ka) = -log10(1.659 x 10^-6) = 5.78.\nThis value matches one of the options.",
        'formatted_prompt': """Question: A new compound is synthesized and found to be a monoprotic acid with a molar mass of 248 g/mol. When 0.0050 mol of this acid are dissolved in 0.500 L of water, the pH is measured as 3.89. What is the pKa of this acid?

(A) 3.89
(B) 7.78
(C) 5.78
(D) 2.33
First, provide a step-by-step reasoning for your answer. After your reasoning, conclude with the final answer in the format 'The final answer is (X)'.

Answer: 1. First, calculate the initial concentration of the acid (HA). Concentration = moles / volume = 0.0050 mol / 0.500 L = 0.010 M.
2. Next, calculate the concentration of H+ ions from the pH. [H+] = 10^(-pH) = 10^(-3.89) = 1.288 x 10^-4 M.
3. For a monoprotic acid, the dissociation is HA <=> H+ + A-. At equilibrium, [H+] = [A-] = 1.288 x 10^-4 M, and [HA] = 0.010 - 1.288 x 10^-4 ≈ 0.010 M (since the dissociation is small).
4. Now, calculate the acid dissociation constant, Ka. Ka = ([H+][A-]) / [HA] = (1.288 x 10^-4)^2 / 0.010 = 1.659 x 10^-6.
5. Finally, calculate the pKa. pKa = -log10(Ka) = -log10(1.659 x 10^-6) = 5.78.
This value matches one of the options. The final answer is (C)"""
    },
    {
        'question': 'What is the output of "abc"[::-1] in Python 3?',
        'subject': 'high_school_computer_science',
        'choices': [
            'Error',
            'abc',
            'cba',
            'c'
        ],
        'answer': 2,
        'reasoning': "In Python, the slice notation `[start:stop:step]` is used to slice sequences. The notation `[::-1]` means start from the end, go to the beginning, with a step of -1. This effectively reverses the string. For the string 'abc', reversing it gives 'cba'.",
        'formatted_prompt': """Question: What is the output of "abc"[::-1] in Python 3?

(A) Error
(B) abc
(C) cba
(D) c
First, provide a step-by-step reasoning for your answer. After your reasoning, conclude with the final answer in the format 'The final answer is (X)'.

Answer: In Python, the slice notation `[start:stop:step]` is used to slice sequences. The notation `[::-1]` means start from the end, go to the beginning, with a step of -1. This effectively reverses the string. For the string 'abc', reversing it gives 'cba'. The final answer is (C)"""
    }
]

def get_mmlu_cot_few_shot_examples(n_shots=None):
    """
    获取MMLU的CoT few-shot examples
    """
    if n_shots is None or n_shots >= len(MMLU_COT_FEW_SHOT_EXAMPLES):
        return MMLU_COT_FEW_SHOT_EXAMPLES.copy()
    
    return MMLU_COT_FEW_SHOT_EXAMPLES[:n_shots].copy()