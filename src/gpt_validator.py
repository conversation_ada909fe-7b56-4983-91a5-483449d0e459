"""
GPT置信度验证器 - 使用GPT API验证模型的置信度分数
"""

import logging
import random
from typing import Dict, Any, Optional
try:
    import openai
except ImportError:
    openai = None

logger = logging.getLogger(__name__)

class GPTConfidenceValidator:
    """使用GPT API验证置信度分数的类"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", 
                 base_url: Optional[str] = None, timeout: int = 30):
        """
        初始化GPT置信度验证器
        
        Args:
            api_key: OpenAI API密钥
            model: 使用的GPT模型名称
            base_url: 自定义API基础URL（可选）
            timeout: 请求超时时间
        """
        if openai is None:
            raise ImportError("需要安装openai包: pip install openai")
            
        self.api_key = api_key
        self.model = model
        self.timeout = timeout
        
        # 配置OpenAI客户端
        if base_url:
            self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
        else:
            self.client = openai.OpenAI(api_key=api_key)
    
    def validate_answer_confidence(self, question: str, answer: str, 
                                   model_confidence: float) -> Dict[str, Any]:
        """
        使用GPT API验证答案置信度
        
        Args:
            question: 原始问题
            answer: 模型给出的答案
            model_confidence: 模型自身的置信度分数
            
        Returns:
            包含GPT验证结果的字典
        """
        try:
            prompt = f"""Please evaluate the confidence of the following answer to a question.

Question: {question}

Answer: {answer}

Model's self-reported confidence: {model_confidence:.3f}

Please rate how confident you are that this answer is correct on a scale from 0.0 to 1.0, where:
- 0.0 means completely incorrect/unreliable
- 0.5 means uncertain
- 1.0 means completely correct/reliable

Consider factors like:
1. Factual accuracy of the answer
2. Logical consistency 
3. Completeness of the response
4. Appropriateness for the question asked

Respond with just a single number between 0.0 and 1.0."""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1,
                timeout=self.timeout
            )
            
            gpt_confidence_str = response.choices[0].message.content.strip()
            
            # 尝试解析置信度分数
            try:
                gpt_confidence = float(gpt_confidence_str)
                gpt_confidence = max(0.0, min(1.0, gpt_confidence))  # 确保在0-1范围内
            except ValueError:
                logger.warning(f"无法解析GPT置信度分数: {gpt_confidence_str}，使用默认值0.5")
                gpt_confidence = 0.5
            
            return {
                "gpt_confidence": gpt_confidence,
                "model_confidence": model_confidence,
                "confidence_diff": abs(gpt_confidence - model_confidence),
                "gpt_response": gpt_confidence_str,
                "status": "success"
            }
            
        except Exception as e:
            logger.warning(f"GPT置信度验证失败: {e}")
            return {
                "gpt_confidence": 0.5,
                "model_confidence": model_confidence,
                "confidence_diff": abs(0.5 - model_confidence),
                "gpt_response": f"Error: {str(e)}",
                "status": "error"
            }
    
    def validate_path_confidence(self, question: str, reasoning_path: str, 
                                 model_confidence: float) -> Dict[str, Any]:
        """
        使用GPT API验证推理路径置信度
        
        Args:
            question: 原始问题
            reasoning_path: 推理路径
            model_confidence: 模型自身的路径置信度分数
            
        Returns:
            包含GPT验证结果的字典
        """
        try:
            prompt = f"""Please evaluate the quality and reliability of the following reasoning process.

Question: {question}

Reasoning Process: {reasoning_path}

Model's self-reported path confidence: {model_confidence:.3f}

Please rate how confident you are that this reasoning process is sound and logical on a scale from 0.0 to 1.0, where:
- 0.0 means completely flawed/illogical
- 0.5 means uncertain or partially correct
- 1.0 means completely sound/logical

Consider factors like:
1. Logical consistency and flow
2. Correctness of intermediate steps
3. Appropriateness of the reasoning approach
4. Clarity and coherence

Respond with just a single number between 0.0 and 1.0."""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1,
                timeout=self.timeout
            )
            
            gpt_confidence_str = response.choices[0].message.content.strip()
            
            # 尝试解析置信度分数
            try:
                gpt_confidence = float(gpt_confidence_str)
                gpt_confidence = max(0.0, min(1.0, gpt_confidence))  # 确保在0-1范围内
            except ValueError:
                logger.warning(f"无法解析GPT路径置信度分数: {gpt_confidence_str}，使用默认值0.5")
                gpt_confidence = 0.5
            
            return {
                "gpt_path_confidence": gpt_confidence,
                "model_path_confidence": model_confidence,
                "path_confidence_diff": abs(gpt_confidence - model_confidence),
                "gpt_response": gpt_confidence_str,
                "status": "success"
            }
            
        except Exception as e:
            logger.warning(f"GPT路径置信度验证失败: {e}")
            return {
                "gpt_path_confidence": 0.5,
                "model_path_confidence": model_confidence,
                "path_confidence_diff": abs(0.5 - model_confidence),
                "gpt_response": f"Error: {str(e)}",
                "status": "error"
            }
