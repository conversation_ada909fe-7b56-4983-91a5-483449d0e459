import json
import re
from typing import Dict, List, Any, Optional
from datasets import load_dataset
import logging

logger = logging.getLogger(__name__)

class DatasetProcessor:
    """数据集处理基类"""
    
    def __init__(self, dataset_name: str):
        self.dataset_name = dataset_name
        self.dataset = None
    
    def load_dataset(self):
        """加载数据集"""
        raise NotImplementedError
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None) -> str:
        """格式化输入prompt"""
        raise NotImplementedError
    
    def get_few_shot_examples(self, n_shots: int, exclude_indices: Optional[set] = None) -> List[Dict[str, Any]]:
        """获取few-shot示例"""
        return []  # 基类默认返回空列表
    
    def extract_answer(self, response: str) -> str:
        """从响应中提取答案"""
        raise NotImplementedError
    
    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查答案是否正确"""
        raise NotImplementedError

class MMLUProProcessor(DatasetProcessor):
    """MMLU-Pro数据集处理器"""
    
    def __init__(self):
        super().__init__("MMLU-Pro")
        self.load_dataset()
    
    def load_dataset(self):
        """加载MMLU-Pro数据集"""
        try:
            # 加载测试集
            self.dataset = load_dataset("TIGER-Lab/MMLU-Pro", split="test")
            try:
                logger.info(f"Loaded MMLU-Pro dataset with {len(self.dataset)} examples")
            except TypeError:
                logger.info("Loaded MMLU-Pro dataset")
        except Exception as e:
            logger.error(f"Error loading MMLU-Pro dataset: {e}")
            raise
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None) -> str:
        """格式化MMLU-Pro的prompt"""
        # 简化实现：暂时不支持few-shot，只使用原有逻辑
        question = example["question"]
        options = example["options"]
        
        # 构建选项字符串
        options_str = ""
        for i, option in enumerate(options):
            options_str += f"({chr(65 + i)}) {option}\n"
        
        prompt = f"""Question: {question}

{options_str}
Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, D, E, F, G, H, I, or J).

Answer:"""
        
        return prompt
    
    def extract_answer(self, response: str) -> str:
        """从响应中提取答案选项"""
        # 查找第一个出现的选项字母
        pattern = r'\b([A-J])\b'
        matches = re.findall(pattern, response.upper())
        
        if matches:
            return matches[0]
        
        # 如果没有找到，尝试查找括号中的选项
        pattern = r'\(([A-J])\)'
        matches = re.findall(pattern, response.upper())
        
        if matches:
            return matches[0]
        
        return ""
    
    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查答案是否正确"""
        return pred_answer.upper() == gold_answer.upper()

class GSM8KProcessor(DatasetProcessor):
    """GSM8K数据集处理器"""
    
    def __init__(self):
        super().__init__("GSM8K")
        self.load_dataset()
    
    def load_dataset(self):
        """加载GSM8K数据集"""
        try:
            # 加载测试集
            self.dataset = load_dataset("gsm8k", "main", split="test")
            logger.info(f"Loaded GSM8K dataset with {len(self.dataset)} examples")
        except Exception as e:
            logger.error(f"Error loading GSM8K dataset: {e}")
            raise
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None) -> str:
        """格式化GSM8K的prompt"""
        # 简化实现：暂时不支持few-shot，只使用原有逻辑
        question = example["question"]
        
        prompt = f"""Question: {question}

Please solve this step by step and provide the numerical answer at the end.

Answer:"""
        
        return prompt
    
    def extract_answer(self, response: str) -> str:
        """从响应中提取数值答案"""
        # 查找最后一个数字（可能包含小数点和负号）
        numbers = re.findall(r'-?\d+(?:\.\d+)?', response)
        
        if numbers:
            # 返回最后一个数字
            return numbers[-1]
        
        return ""
    
    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查数值答案是否正确"""
        try:
            # 从gold_answer中提取数值（GSM8K的答案格式为"#### 数值"）
            gold_nums = re.findall(r'-?\d+(?:\.\d+)?', gold_answer)
            if not gold_nums:
                return False
            
            gold_val = float(gold_nums[-1])
            pred_val = float(pred_answer)
            
            # 允许小的浮点误差
            return abs(gold_val - pred_val) < 1e-6
            
        except (ValueError, TypeError):
            return False

class MMLUProcessor(DatasetProcessor):
    """标准MMLU数据集处理器"""
    
    def __init__(self):
        super().__init__("MMLU")
        self.load_dataset()
    
    def load_dataset(self):
        """加载MMLU数据集"""
        try:
            # 加载测试集
            dataset = load_dataset("cais/mmlu", "all", split="test")
            self.dataset = dataset
            try:
                logger.info(f"Loaded MMLU dataset with {len(dataset)} examples")
            except TypeError:
                logger.info("Loaded MMLU dataset (iterable)")
        except Exception as e:
            logger.error(f"Error loading MMLU dataset: {e}")
            raise
    
    def format_prompt(self, example: Dict[str, Any], few_shot_examples: Optional[List[Dict[str, Any]]] = None) -> str:
        """格式化MMLU的prompt"""
        # 简化实现：暂时不支持few-shot，只使用原有逻辑
        question = example["question"]
        choices = example["choices"]
        
        # 构建选项字符串
        options_str = ""
        for i, choice in enumerate(choices):
            options_str += f"({chr(65 + i)}) {choice}\n"
        
        prompt = f"""Question: {question}

{options_str}
Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, or D).

Answer:"""
        
        return prompt
    
    def extract_answer(self, response: str) -> str:
        """从响应中提取答案选项"""
        # 查找第一个出现的选项字母
        pattern = r'\b([A-D])\b'
        matches = re.findall(pattern, response.upper())
        
        if matches:
            return matches[0]
        
        # 如果没有找到，尝试查找括号中的选项
        pattern = r'\(([A-D])\)'
        matches = re.findall(pattern, response.upper())
        
        if matches:
            return matches[0]
        
        return ""
    
    def check_answer(self, pred_answer: str, gold_answer: str) -> bool:
        """检查答案是否正确"""
        # MMLU的答案可能是数字格式(0,1,2,3)或字母格式，需要统一处理
        if str(gold_answer).isdigit():
            # 数字格式，转换为字母
            gold_letter = chr(65 + int(gold_answer))  # 0->A, 1->B, 2->C, 3->D
        else:
            # 已经是字母格式
            gold_letter = str(gold_answer).upper()
        
        return pred_answer.upper() == gold_letter

def get_dataset_processor(dataset_name: str) -> DatasetProcessor:
    """获取数据集处理器"""
    if dataset_name.lower() == "mmlu-pro":
        return MMLUProProcessor()
    elif dataset_name.lower() == "mmlu":
        return MMLUProcessor()
    elif dataset_name.lower() == "gsm8k":
        return GSM8KProcessor()
    else:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

def sample_dataset(processor: DatasetProcessor, num_samples: Optional[int] = None) -> List[Dict[str, Any]]:
    """采样数据集"""
    if processor.dataset is None:
        raise ValueError("Dataset is not loaded")
    
    # 转换为列表以确保兼容性
    dataset_list = list(processor.dataset)
    
    if num_samples is None or num_samples >= len(dataset_list):
        return dataset_list
    
    # 随机采样
    import random
    indices = random.sample(range(len(dataset_list)), num_samples)
    return [dataset_list[i] for i in indices]
