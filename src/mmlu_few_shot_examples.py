#!/usr/bin/env python3
"""
MMLU数据集的默认few-shot examples
从MMLU dev数据集中选择的10个多样性样本，覆盖不同学科
"""

# MMLU默认few-shot examples (10个多样性样本)
MMLU_DEFAULT_FEW_SHOT_EXAMPLES = [
    {
        'question': 'The complex question fallacy consists of',
        'subject': 'logical_fallacies',
        'choices': [
            "arguing something is inferior just because it doesn't do something it was never intended to do.",
            'including more than one claim in the proposition and treating proof for one claim as proof for all the claims.',
            'drawing a conclusion before examining the evidence, and only considering evidence that supports that conclusion.',
            'asking a question that includes either an unproven assumption or more than one question, thus making a straightforward yes or no answer meaningless.'
        ],
        'answer': 3
    },
    {
        'question': 'Which of the following cases established the precedent that a defendant must be informed of the right to remain silent, the right to a lawyer, and protection from self-incrimination?',
        'subject': 'high_school_government_and_politics',
        'choices': [
            'Weeks v. United States',
            '<PERSON>ts v. Brady',
            'Mapp v. Ohio',
            'Miranda v. Arizona'
        ],
        'answer': 3
    },
    {
        'question': "What should a public relations media practitioner do if she does not know the answer to a reporter's question?",
        'subject': 'public_relations',
        'choices': [
            'Give the reporter other information she is certain is correct.',
            "Say that the information is 'off the record' and will be disseminated later.",
            "Say 'I don't know' and promise to provide the information later.",
            "Say 'no comment,' rather than appear uninformed."
        ],
        'answer': 2
    },
    {
        'question': 'Exploitation of the Heartbleed bug permits',
        'subject': 'computer_security',
        'choices': [
            'overwriting cryptographic keys in memory',
            'a kind of code injection',
            'a read outside bounds of a buffer',
            'a format string attack'
        ],
        'answer': 2
    },
    {
        'question': ' Select the best translation into predicate logic: No people drive on Mars.',
        'subject': 'formal_logic',
        'choices': [
            '~Pd',
            '(∀x)(Px ∨ ~Dx)',
            '(∀x)(Px ⊃ ~Dx)',
            '~Dp'
        ],
        'answer': 2
    },
    {
        'question': 'What is the output of "abc"[::-1] in Python 3?',
        'subject': 'high_school_computer_science',
        'choices': [
            'Error',
            'abc',
            'cba',
            'c'
        ],
        'answer': 2
    },
    {
        'question': 'How can we best describe the relationship between the state-centric approach and the concept of human security?',
        'subject': 'security_studies',
        'choices': [
            'There are such wide divisions within the human security framework regarding the nature of threats and referent objects that no widely applicable comparisons between state-centric approaches and human security can be drawn.',
            'By adopting the framework of human security, the limitations of the realist state-centric approach become evident. Whilst human security defines the referent object as the person or population, state-centric approaches prioritise the security of the state, de-prioritizing the pursuit of human security.',
            'The state-centric approach to security is a faction of human security, usually defined within the broad school of human security. By being state-centric this approach prioritises the individual as the referent object in security studies.',
            'Both the state-centric and human-centric approaches to security are mutually exclusive and offer a sufficient analytic framework with which to understand the international security system. It is therefore the role of security analysts to determine which of these substantial concepts is correct, and which should be discarded.'
        ],
        'answer': 1
    },
    {
        'question': 'Which of the following is a morphological characteristic of the paramyxoviruses.',
        'subject': 'virology',
        'choices': [
            'Fragile viruses often visualised with RNA spewing from the inside',
            'Elongate viruses',
            'Icosahedral viruses with envelope',
            'Very large viruses'
        ],
        'answer': 0
    },
    {
        'question': 'The most common disorder among men who seek sexual therapy is:',
        'subject': 'human_sexuality',
        'choices': [
            'premature ejaculation',
            'inhibited ejaculation',
            'erectile disorder',
            'ejaculatory disorder'
        ],
        'answer': 2
    },
    {
        'question': 'A new compound is synthesized and found to be a monoprotic acid with a molar mass of 248 g/mol. When 0.0050 mol of this acid are dissolved in 0.500 L of water, the pH is measured as 3.89. What is the pKa of this acid?',
        'subject': 'high_school_chemistry',
        'choices': [
            '3.89',
            '7.78',
            '5.78',
            '2.33'
        ],
        'answer': 2
    }
]

def get_mmlu_few_shot_examples(n_shots=None):
    """
    获取MMLU的few-shot examples
    
    Args:
        n_shots: 需要的few-shot数量，如果为None则返回所有10个
        
    Returns:
        List[Dict]: few-shot examples列表
    """
    if n_shots is None or n_shots >= len(MMLU_DEFAULT_FEW_SHOT_EXAMPLES):
        return MMLU_DEFAULT_FEW_SHOT_EXAMPLES.copy()
    
    # 如果需要的数量少于10个，返回前n_shots个
    return MMLU_DEFAULT_FEW_SHOT_EXAMPLES[:n_shots].copy()

def format_mmlu_few_shot_prompt(examples):
    """
    将few-shot examples格式化为prompt字符串
    
    Args:
        examples: few-shot examples列表
        
    Returns:
        str: 格式化的prompt字符串
    """
    prompt_parts = []
    
    for example in examples:
        question = example['question']
        choices = example['choices']
        answer = example['answer']
        
        # 构建选项字符串
        options_str = ""
        for i, choice in enumerate(choices):
            options_str += f"({chr(65 + i)}) {choice}\n"
        
        # 转换答案格式
        if isinstance(answer, int):
            answer_letter = chr(65 + answer)
        else:
            answer_letter = str(answer).upper()
        
        # 构建单个example的prompt
        example_prompt = f"""Question: {question}

{options_str}Please select the correct answer from the options above. Provide your answer as a single letter (A, B, C, or D).

Answer: {answer_letter}

"""
        prompt_parts.append(example_prompt)
    
    return "".join(prompt_parts)

def get_subject_distribution():
    """
    获取few-shot examples的学科分布
    
    Returns:
        Dict[str, int]: 学科分布字典
    """
    from collections import Counter
    subjects = [example['subject'] for example in MMLU_DEFAULT_FEW_SHOT_EXAMPLES]
    return dict(Counter(subjects))

def print_few_shot_summary():
    """打印few-shot examples的摘要信息"""
    print("📊 MMLU Few-shot Examples 摘要:")
    print(f"   总数量: {len(MMLU_DEFAULT_FEW_SHOT_EXAMPLES)} 个")
    
    subject_dist = get_subject_distribution()
    print(f"   覆盖学科: {len(subject_dist)} 个")
    
    print("\n📋 学科分布:")
    for i, (subject, count) in enumerate(subject_dist.items(), 1):
        print(f"   {i:2d}. {subject}: {count} 个样本")
    
    print("\n🎯 样本预览:")
    for i, example in enumerate(MMLU_DEFAULT_FEW_SHOT_EXAMPLES[:3], 1):
        print(f"   {i}. [{example['subject']}] {example['question'][:60]}...")

if __name__ == "__main__":
    # 测试代码
    print_few_shot_summary()
    
    print("\n" + "="*80)
    print("📝 Few-shot Prompt 示例 (前3个):")
    print("="*80)
    
    sample_examples = get_mmlu_few_shot_examples(3)
    sample_prompt = format_mmlu_few_shot_prompt(sample_examples)
    print(sample_prompt)
