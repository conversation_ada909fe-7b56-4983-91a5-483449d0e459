import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, Any
import os

from .evaluator import ModelResults

def create_summary_report(all_results: Dict[str, Dict[str, ModelResults]], output_file: str):
    """创建汇总报告"""
    
    report_lines = []
    report_lines.append("# Self-Consistency Baseline Evaluation Report")
    report_lines.append("")
    report_lines.append("## 评估配置")
    report_lines.append("- Sampling次数: 5")
    report_lines.append("- Temperature: 0.7") 
    report_lines.append("- Top-p: 0.9")
    report_lines.append("- 评估任务: MMLU-Pro, GSM8K")
    report_lines.append("")
    
    # 创建结果表格
    report_lines.append("## 评估结果汇总")
    report_lines.append("")
    
    # 准确率表格
    report_lines.append("### 准确率 (Accuracy)")
    report_lines.append("")
    report_lines.append("| 模型 | MMLU-Pro | GSM8K |")
    report_lines.append("|------|----------|-------|")
    
    for model_name, model_results in all_results.items():
        mmlu_acc = model_results.get("mmlu-pro", {}).accuracy if "mmlu-pro" in model_results else 0
        gsm8k_acc = model_results.get("gsm8k", {}).accuracy if "gsm8k" in model_results else 0
        
        report_lines.append(f"| {model_name} | {mmlu_acc:.4f} | {gsm8k_acc:.4f} |")
    
    report_lines.append("")
    
    # Token使用表格
    report_lines.append("### 平均Token数")
    report_lines.append("")
    report_lines.append("| 模型 | MMLU-Pro | GSM8K |")
    report_lines.append("|------|----------|-------|")
    
    for model_name, model_results in all_results.items():
        mmlu_tokens = model_results.get("mmlu-pro", {}).avg_tokens_per_sample if "mmlu-pro" in model_results else 0
        gsm8k_tokens = model_results.get("gsm8k", {}).avg_tokens_per_sample if "gsm8k" in model_results else 0
        
        report_lines.append(f"| {model_name} | {mmlu_tokens:.1f} | {gsm8k_tokens:.1f} |")
    
    report_lines.append("")
    
    # 详细统计
    report_lines.append("## 详细统计")
    report_lines.append("")
    
    for model_name, model_results in all_results.items():
        report_lines.append(f"### {model_name}")
        report_lines.append("")
        
        for dataset_name, results in model_results.items():
            report_lines.append(f"#### {dataset_name.upper()}")
            report_lines.append(f"- 总样本数: {results.total_samples}")
            report_lines.append(f"- 正确样本数: {results.correct_samples}")
            report_lines.append(f"- 准确率: {results.accuracy:.4f}")
            report_lines.append(f"- 总Token数: {results.total_tokens:,}")
            report_lines.append(f"- 平均每样本Token数: {results.avg_tokens_per_sample:.1f}")
            report_lines.append(f"- 总推理时间: {results.total_inference_time:.2f}s")
            report_lines.append(f"- 平均每样本推理时间: {results.avg_inference_time:.2f}s")
            report_lines.append("")
    
    # Self-Consistency分析
    report_lines.append("## Self-Consistency 分析")
    report_lines.append("")
    
    for model_name, model_results in all_results.items():
        report_lines.append(f"### {model_name}")
        
        for dataset_name, results in model_results.items():
            # 分析self-consistency的效果
            consistent_correct = 0  # 答案一致且正确的样本
            inconsistent_correct = 0  # 答案不一致但最终正确的样本
            
            for sample in results.sample_results:
                unique_answers = set(sample.extracted_answers)
                if len(unique_answers) == 1:
                    # 答案完全一致
                    if sample.is_correct:
                        consistent_correct += 1
                else:
                    # 答案不一致，但通过majority voting得到正确答案
                    if sample.is_correct:
                        inconsistent_correct += 1
            
            total_correct = results.correct_samples
            consistency_benefit = inconsistent_correct / total_correct if total_correct > 0 else 0
            
            report_lines.append(f"#### {dataset_name.upper()}")
            report_lines.append(f"- 完全一致且正确: {consistent_correct}")
            report_lines.append(f"- 不一致但通过投票正确: {inconsistent_correct}")
            report_lines.append(f"- Self-consistency受益比例: {consistency_benefit:.2%}")
            report_lines.append("")
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

def plot_results(all_results: Dict[str, Dict[str, ModelResults]], output_file: str):
    """创建结果可视化图表"""
    
    # 准备数据
    data = []
    for model_name, model_results in all_results.items():
        for dataset_name, results in model_results.items():
            data.append({
                'Model': model_name,
                'Dataset': dataset_name.upper(),
                'Accuracy': results.accuracy,
                'Avg_Tokens': results.avg_tokens_per_sample
            })
    
    df = pd.DataFrame(data)
    
    if df.empty:
        print("No data to plot")
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Self-Consistency Baseline Evaluation Results', fontsize=16, fontweight='bold')
    
    # 1. 准确率条形图
    ax1 = axes[0, 0]
    accuracy_pivot = df.pivot(index='Model', columns='Dataset', values='Accuracy')
    accuracy_pivot.plot(kind='bar', ax=ax1, width=0.8)
    ax1.set_title('Accuracy by Model and Dataset')
    ax1.set_ylabel('Accuracy')
    ax1.set_xlabel('Model')
    ax1.legend(title='Dataset')
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. Token使用条形图
    ax2 = axes[0, 1]
    tokens_pivot = df.pivot(index='Model', columns='Dataset', values='Avg_Tokens')
    tokens_pivot.plot(kind='bar', ax=ax2, width=0.8)
    ax2.set_title('Average Tokens per Sample')
    ax2.set_ylabel('Tokens')
    ax2.set_xlabel('Model')
    ax2.legend(title='Dataset')
    ax2.tick_params(axis='x', rotation=45)
    
    # 3. 准确率热力图
    ax3 = axes[1, 0]
    sns.heatmap(accuracy_pivot, annot=True, fmt='.4f', cmap='Blues', ax=ax3)
    ax3.set_title('Accuracy Heatmap')
    ax3.set_xlabel('Dataset')
    ax3.set_ylabel('Model')
    
    # 4. 效率分析（准确率 vs Token使用）
    ax4 = axes[1, 1]
    for dataset in df['Dataset'].unique():
        dataset_data = df[df['Dataset'] == dataset]
        ax4.scatter(dataset_data['Avg_Tokens'], dataset_data['Accuracy'], 
                   label=dataset, s=100, alpha=0.7)
        
        # 添加模型标签
        for _, row in dataset_data.iterrows():
            ax4.annotate(row['Model'], (row['Avg_Tokens'], row['Accuracy']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax4.set_xlabel('Average Tokens per Sample')
    ax4.set_ylabel('Accuracy')
    ax4.set_title('Efficiency Analysis: Accuracy vs Token Usage')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Results visualization saved to {output_file}")

def analyze_self_consistency_patterns(results: ModelResults):
    """分析self-consistency模式"""
    
    patterns = {
        'unanimous_correct': 0,  # 全部一致且正确
        'unanimous_wrong': 0,    # 全部一致但错误
        'majority_correct': 0,   # 多数正确
        'majority_wrong': 0,     # 多数错误
        'tie': 0                 # 平票
    }
    
    for sample in results.sample_results:
        answers = sample.extracted_answers
        unique_answers = list(set(answers))
        
        if len(unique_answers) == 1:
            # 全部一致
            if sample.is_correct:
                patterns['unanimous_correct'] += 1
            else:
                patterns['unanimous_wrong'] += 1
        else:
            # 需要投票决定
            answer_counts = {ans: answers.count(ans) for ans in unique_answers}
            max_count = max(answer_counts.values())
            
            if list(answer_counts.values()).count(max_count) > 1:
                # 平票情况
                patterns['tie'] += 1
            else:
                # 有明确多数
                if sample.is_correct:
                    patterns['majority_correct'] += 1
                else:
                    patterns['majority_wrong'] += 1
    
    return patterns
