#!/usr/bin/env python3
"""
使用vLLM API运行Self-Consistency Baseline评估，并使用GPT API验证置信度分数
"""

import os
os.environ["CUDA_VISIBLE_DEVICES"]='2'
import sys
import json
import logging
import time
import argparse
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List

# 尝试导入openai，如果失败则设为None
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    openai = None
    HAS_OPENAI = False

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.local_datasets import get_local_dataset_processor, sample_local_dataset
from src.vllm_models import VLLMModelWrapper, VLLMConfig, create_vllm_models_config, test_vllm_connection
from src.vllm_evaluator import VLLMSelfConsistencyEvaluator, save_vllm_results

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_gpt_validator(api_key: str, model: str = "gpt-3.5-turbo", base_url: Optional[str] = None):
    """创建GPT验证器的工厂函数"""
    if not HAS_OPENAI:
        raise ImportError("需要安装openai包: pip install openai")
    
    class GPTValidator:
        def __init__(self):
            if base_url:
                self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
            else:
                self.client = openai.OpenAI(api_key=api_key)
            self.model = model
        
        def validate_answer_confidence(self, question: str, answer: str, model_confidence: float) -> Dict[str, Any]:
            try:
                prompt = f"""Please evaluate the confidence of the following answer to a question.

Question: {question}

Answer: {answer}

Model's self-reported confidence: {model_confidence:.3f}

Please rate how confident you are that this answer is correct on a scale from 0.0 to 1.0, where:
- 0.0 means completely incorrect/unreliable
- 0.5 means uncertain
- 1.0 means completely correct/reliable

Respond with just a single number between 0.0 and 1.0."""

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=10,
                    temperature=0.1,
                    timeout=30
                )
                
                gpt_confidence_str = response.choices[0].message.content.strip()
                
                try:
                    gpt_confidence = float(gpt_confidence_str)
                    gpt_confidence = max(0.0, min(1.0, gpt_confidence))
                except ValueError:
                    logger.warning(f"无法解析GPT置信度分数: {gpt_confidence_str}，使用默认值0.5")
                    gpt_confidence = 0.5
                
                return {
                    "gpt_confidence": gpt_confidence,
                    "model_confidence": model_confidence,
                    "confidence_diff": abs(gpt_confidence - model_confidence),
                    "gpt_response": gpt_confidence_str,
                    "status": "success"
                }
                
            except Exception as e:
                logger.warning(f"GPT置信度验证失败: {e}")
                return {
                    "gpt_confidence": 0.5,
                    "model_confidence": model_confidence,
                    "confidence_diff": abs(0.5 - model_confidence),
                    "gpt_response": f"Error: {str(e)}",
                    "status": "error"
                }
        
        def validate_path_confidence(self, question: str, reasoning_path: str, model_confidence: float) -> Dict[str, Any]:
            try:
                prompt = f"""Please evaluate the quality and reliability of the following reasoning process.

Question: {question}

Reasoning Process: {reasoning_path}

Model's self-reported path confidence: {model_confidence:.3f}

Please rate how confident you are that this reasoning process is sound and logical on a scale from 0.0 to 1.0, where:
- 0.0 means completely flawed/illogical
- 0.5 means uncertain or partially correct  
- 1.0 means completely sound/logical

Respond with just a single number between 0.0 and 1.0."""

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=10,
                    temperature=0.1,
                    timeout=30
                )
                
                gpt_confidence_str = response.choices[0].message.content.strip()
                
                try:
                    gpt_confidence = float(gpt_confidence_str)
                    gpt_confidence = max(0.0, min(1.0, gpt_confidence))
                except ValueError:
                    logger.warning(f"无法解析GPT路径置信度分数: {gpt_confidence_str}，使用默认值0.5")
                    gpt_confidence = 0.5
                
                return {
                    "gpt_path_confidence": gpt_confidence,
                    "model_path_confidence": model_confidence,
                    "path_confidence_diff": abs(gpt_confidence - model_confidence),
                    "gpt_response": gpt_confidence_str,
                    "status": "success"
                }
                
            except Exception as e:
                logger.warning(f"GPT路径置信度验证失败: {e}")
                return {
                    "gpt_path_confidence": 0.5,
                    "model_path_confidence": model_confidence,
                    "path_confidence_diff": abs(0.5 - model_confidence),
                    "gpt_response": f"Error: {str(e)}",
                    "status": "error"
                }
    
    return GPTValidator()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="使用vLLM API运行Self-Consistency Baseline评估")
    
    # 基本参数
    parser.add_argument("--models", nargs="+", 
                        # default=["ministral-7b", "/data/gemma-2-2b", "qwen2.5-3b"],
                        #default=["gemma-2-2b"],
                        default=["Meta-Llama-3.1-8B-Instruct"],
                        # default=["/data/Mistral-7B-Instruct-v0.3"],
                       help="要评估的模型名称列表（vLLM服务器上的模型名称）")
    parser.add_argument("--datasets", nargs="+", default=["GSM8K"], # default=["MMLU", "MMLU-Pro", "GSM8K"],
                       help="要评估的数据集列表")
    parser.add_argument("--num_samples", type=int, default=100,
                       help="每个数据集的样本数量（None表示使用全部样本）")
    # 新增: CoT参数
    parser.add_argument("--use_cot", default=True,
                       help="启用Chain-of-Thought prompting和答案提取")
    # 置信度分数参数 ### New
    parser.add_argument("--confidence_temp", type=float, default=0.5,
                       help="用于置信度分数softmax的温度。")
    # --- GPT验证相关参数 ---
    parser.add_argument("--use_gpt_validation", action="store_true", default=True,
                       help="启用GPT API验证置信度分数")
    parser.add_argument("--gpt_api_key", type=str,      default='013b1c0032d74e1e8df0c80a654d3b6dfe54a78331634279940b33864f837d66',
                       help="OpenAI API密钥（如果未提供将从环境变量OPENAI_API_KEY读取）")
    parser.add_argument("--gpt_model", type=str, default="DeepSeek-R1-671B",
                       help="用于验证的GPT模型名称")
    parser.add_argument("--gpt_base_url", type=str, default='https://gpt-api.hkust-gz.edu.cn/v1/chat/completions',
                       help="自定义GPT API基础URL（可选）")
    parser.add_argument("--gpt_validation_sample_rate", type=float, default=0.2,
                       help="GPT验证的采样率（0.0-1.0），用于控制验证成本")
    # --- 新增参数结束 ---
    # --- 新增参数 ---
    # parser.add_argument("--confidence_threshold", type=float, default=0.3,
    #                    help="置信度分数阈值。低于此值的回答将被排除在投票之外。默认为0.0（不过滤）。")
    # --- 新增参数结束 ---
    ## --- NEW/MODIFIED ARGUMENTS --- ##
    parser.add_argument("--use_path_confidence",default=True,
                       help="启用路径置信度评分。最终置信度=答案置信度*路径置信度。")
    parser.add_argument("--combined_confidence_threshold", type=float, default=0.3,
                       help="组合置信度分数阈值。低于此值的回答将被排除在投票之外。默认为0.0（不过滤）。")
    # Few-shot参数
    parser.add_argument("--few_shot", type=int, default=5,
                       help="Few-shot示例数量（0表示zero-shot）")
    # Self-consistency参数
    parser.add_argument("--n_consistency_samples", type=int, default=5,
                       help="Self-consistency采样次数")
    parser.add_argument("--temperature", type=float, default=0.6,
                       help="采样温度")
    parser.add_argument("--top_p", type=float, default=0.9,
                       help="Top-p采样参数")
    # parser.add_argument("--max_tokens", type=int, default=512,
    parser.add_argument("--max_tokens", type=int, default=512,
                       help="最大生成token数")
    # vLLM服务器配置
    parser.add_argument("--base_urls", nargs="+", 
                    #   default=["http://localhost:8079"], # gemma-2-2b
                    #    default=["http://localhost:8888"],   # ministral-7b
                        default=["http://localhost:8882"],   # Meta-Llama-3.1-8B-Instruct
                       help="vLLM服务器地址列表")
    parser.add_argument("--api_keys", nargs="+",
                       help="API密钥列表（如果需要）")
    # 输出配置
    parser.add_argument("--output_dir", default="results",
                       help="结果输出目录")
    parser.add_argument("--quick_test", action="store_true",
                       help="快速测试模式（少量样本）")
    parser.add_argument("--keep_progress_files", action="store_true",
                       help="保留中间进度文件（默认会在完成后删除）")
    return parser.parse_args()

def save_paths_to_json(results, model_key: str, dataset_name: str, output_dir: Path, timestamp: str):
    """将所有路径详细信息保存到JSON文件（紧凑格式，避免重复）"""
    try:
        # 生成安全的文件名
        safe_model_name = model_key.replace("/", "_").replace("\\", "_")
        json_file = output_dir / f"paths_detail_{safe_model_name}_{dataset_name}_{timestamp}.json"
        
        logger.info(f"📊 开始保存路径详细信息到JSON: {json_file}")
        
        # 构建紧凑的数据结构
        paths_data = {
            "metadata": {
                "model_name": model_key,
                "dataset_name": dataset_name,
                "timestamp": timestamp,
                "total_questions": len(results.sample_results),
                "total_paths": sum(len(sr.responses) for sr in results.sample_results)
            },
            "questions": []
        }
        
        # 统计信息
        total_paths = 0
        
        # 处理每个问题样本
        for sample_result in results.sample_results:
            # 问题级别的基本信息（只保存一次）
            question_data = {
                "question_id": sample_result.question_id,
                "question": sample_result.question,
                "gold_answer": sample_result.gold_answer,
                "final_answer": sample_result.final_answer,
                "is_correct": sample_result.is_correct,
                "paths": []
            }
            
            # 每个路径的详细信息
            for i in range(len(sample_result.responses)):
                # 分离推理路径和答案部分
                reasoning_path, answer_part = extract_reasoning_and_answer(sample_result.responses[i])
                
                path_data = {
                    "path_index": i,
                    "response_text": sample_result.responses[i],
                    "extracted_answer": sample_result.extracted_answers[i] if i < len(sample_result.extracted_answers) else "",
                    "answer_confidence": sample_result.answer_confidence_scores[i] if i < len(sample_result.answer_confidence_scores) else 0.0,
                    "path_confidence": sample_result.path_confidence_scores[i] if i < len(sample_result.path_confidence_scores) else 0.0,
                    "combined_confidence": sample_result.combined_confidence_scores[i] if i < len(sample_result.combined_confidence_scores) else 0.0,
                    "reasoning_path": reasoning_path,
                    "answer_part": answer_part
                }
                question_data["paths"].append(path_data)
                total_paths += 1
            
            paths_data["questions"].append(question_data)
        
        # 保存到JSON文件
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(paths_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 路径详细信息已保存到JSON: {json_file}")
        logger.info(f"📈 统计信息: {len(results.sample_results)} 个问题, {total_paths} 条路径")
        return json_file
        
    except Exception as e:
        logger.error(f"❌ 保存JSON文件时出错: {e}")
        return None

def cleanup_progress_files(output_dir: Path, safe_model_name: str, dataset_name: str):
    """清理中间进度文件"""
    try:
        # 查找并删除进度文件
        progress_pattern = f"progress_{safe_model_name}_{dataset_name}_*.json"
        paths_progress_pattern = f"paths_detail_{safe_model_name}_{dataset_name}_progress_*.json"
        
        progress_files = list(output_dir.glob(progress_pattern))
        paths_progress_files = list(output_dir.glob(paths_progress_pattern))
        
        total_deleted = 0
        for file in progress_files + paths_progress_files:
            file.unlink()
            total_deleted += 1
        
        if total_deleted > 0:
            logger.info(f"🧹 已清理 {total_deleted} 个中间进度文件")
            
    except Exception as e:
        logger.warning(f"清理进度文件时出错: {e}")

def extract_reasoning_and_answer(response: str):
    """从CoT响应中分离推理路径和最终答案 - 辅助函数"""
    import re
    match = re.search(r'The final answer is', response, re.IGNORECASE)
    if match:
        split_point = match.start()
        reasoning_path = response[:split_point].strip()
        answer_part = response[split_point:].strip()
        return reasoning_path, answer_part
    else:
        # 如果找不到标准格式，整个响应被视为推理，答案部分为空
        return response.strip(), ""

def create_custom_vllm_config(model_key: str, base_url: str, model_name: Optional[str] = None, api_key: Optional[str] = None) -> VLLMConfig:
    """创建自定义vLLM配置"""
    return VLLMConfig(
        base_url=base_url,
        model_name=model_name or model_key,
        api_key=api_key
    )

def run_vllm_baseline_evaluation():
    """运行vLLM baseline评估"""
    args = parse_args()
    
    logger.info(f"   - 置信度温度 (T_conf): {args.confidence_temp}") ## NEW ##
    logger.info("🚀 开始使用vLLM API运行Self-Consistency Baseline评估")
    logger.info(f"📝 评估配置:")
    logger.info(f"   - 模型: {args.models}")
    logger.info(f"   - 数据集: {args.datasets}")
    logger.info(f"   - CoT模式: {'启用' if args.use_cot else '禁用'}") # 新增日志
    logger.info(f"   - Few-shot示例数: {args.few_shot}")
    logger.info(f"   - Self-consistency采样次数: {args.n_consistency_samples}")
    logger.info(f"   - Temperature: {args.temperature}")
    logger.info(f"   - Top-p: {args.top_p}")
    logger.info(f"   - 最大tokens: {args.max_tokens}")
    logger.info(f"   - 路径置信度评分: {'启用' if args.use_path_confidence else '禁用'}")
    if args.use_path_confidence:
        logger.info(f"   - 组合置信度阈值: {args.combined_confidence_threshold}")
    logger.info(f"   - GPT验证: {'启用' if args.use_gpt_validation else '禁用'}")
    if args.use_gpt_validation:
        logger.info(f"   - GPT模型: {args.gpt_model}")
        logger.info(f"   - GPT验证采样率: {args.gpt_validation_sample_rate}")
    logger.info(f"   - 实时保存: 启用（每10个样本保存进度）")
    logger.info(f"   - 保留进度文件: {'是' if args.keep_progress_files else '否'}")
    
    # GPT验证器初始化
    gpt_validator = None
    if args.use_gpt_validation:
        if not HAS_OPENAI:
            logger.error("❌ 启用GPT验证需要安装openai包: pip install openai")
            return
            
        # 获取API密钥
        api_key = args.gpt_api_key or os.getenv('OPENAI_API_KEY')
        if not api_key:
            logger.error("❌ 启用GPT验证需要提供API密钥（--gpt_api_key 或设置环境变量 OPENAI_API_KEY）")
            return
        
        try:
            gpt_validator = create_gpt_validator(
                api_key=api_key,
                model=args.gpt_model,
                base_url=args.gpt_base_url
            )
            logger.info("✅ GPT验证器初始化成功")
        except Exception as e:
            logger.error(f"❌ GPT验证器初始化失败: {e}")
            return
    
    # 快速测试模式
    if args.quick_test:
        logger.info("🏃 启用快速测试模式")
        args.num_samples = 50  # 快速测试只用50个样本
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 测试vLLM服务器连接
    logger.info("🔗 测试vLLM服务器连接...")
    for base_url in args.base_urls:
        if not test_vllm_connection(base_url):
            logger.warning(f"⚠️ 无法连接到vLLM服务器: {base_url}")
    
    # 准备模型配置
    if args.base_urls:
        # 使用自定义配置
        vllm_configs = {}
        for i, model_key in enumerate(args.models):
            base_url = args.base_urls[i] if i < len(args.base_urls) else args.base_urls[0]
            model_name = model_key  # 统一使用models参数作为模型名称
            api_key = args.api_keys[i] if args.api_keys and i < len(args.api_keys) else None
            
            vllm_configs[model_key] = create_custom_vllm_config(
                model_key, base_url, model_name, api_key
            )
    else:
        # 使用预定义配置
        predefined_configs = create_vllm_models_config()
        vllm_configs = {k: v for k, v in predefined_configs.items() if k in args.models}
    
    # 结果存储
    all_results = {
        "timestamp": datetime.now().isoformat(),
        "evaluation_config": {
            "models": args.models,
            "datasets": args.datasets,
            "few_shot": args.few_shot,
            "n_consistency_samples": args.n_consistency_samples,
            "temperature": args.temperature,
            "top_p": args.top_p,
            "max_tokens": args.max_tokens,
            "num_samples": args.num_samples,
            "quick_test": args.quick_test,
            "use_cot": args.use_cot,
            "confidence_temp": args.confidence_temp,
            #"confidence_threshold": args.confidence_threshold # <-- 保存新参数到结果文件
            "use_path_confidence": args.use_path_confidence,
            "combined_confidence_threshold": args.combined_confidence_threshold
        },
        "vllm_configs": {k: {"base_url": v.base_url, "model_name": v.model_name} 
                        for k, v in vllm_configs.items()},
        "results": {}
    }
    
    # 遍历每个模型和数据集
    for model_key in args.models:
        if model_key not in vllm_configs:
            logger.warning(f"⚠️ 模型 {model_key} 没有对应的vLLM配置，跳过")
            continue
        
        logger.info(f"\n🤖 开始评估模型: {model_key}")
        
        try:
            # 创建vLLM模型包装器
            config = vllm_configs[model_key]
            vllm_wrapper = VLLMModelWrapper(config)
            
            # 创建评估器
            evaluator = VLLMSelfConsistencyEvaluator(
                vllm_wrapper=vllm_wrapper,
                n_samples=args.n_consistency_samples,
                temperature=args.temperature,
                top_p=args.top_p,
                max_tokens=args.max_tokens,
                few_shot=args.few_shot,
                confidence_temp=args.confidence_temp, ## NEW ##
                use_cot=args.use_cot,  # 新增 use_cot 参数
                #confidence_threshold=args.confidence_threshold # <-- 传递新参数
                use_path_confidence=args.use_path_confidence, # Pass the flag
                combined_confidence_threshold=args.combined_confidence_threshold # Pass the threshold

            )
            
            # 如果启用GPT验证，设置验证器
            if gpt_validator and hasattr(evaluator, 'set_gpt_validator'):
                evaluator.set_gpt_validator(gpt_validator, args.gpt_validation_sample_rate)
            
            model_results = {}
            
            # 遍历每个数据集
            for dataset_name in args.datasets:
                logger.info(f"\n📊 评估数据集: {dataset_name}")
                
                try:
                    # 加载本地数据集
                    processor = get_local_dataset_processor(dataset_name)
                    
                    # 采样数据
                    test_data = sample_local_dataset(processor, args.num_samples)
                    logger.info(f"📈 使用 {len(test_data)} 个样本进行评估")
                    
                    # 定义进度保存回调函数
                    def save_progress(intermediate_result, current_samples, total_samples):
                        """保存评估进度的回调函数"""
                        try:
                            progress_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            progress_file = output_dir / f"progress_{safe_model_name}_{dataset_name}_{progress_timestamp}.json"
                            
                            # 创建进度数据
                            progress_data = {
                                "progress_info": {
                                    "current_samples": current_samples,
                                    "total_samples": total_samples,
                                    "completion_percentage": (current_samples / total_samples) * 100,
                                    "timestamp": datetime.now().isoformat()
                                },
                                "intermediate_results": {
                                    "accuracy": intermediate_result.accuracy,
                                    "accuracy_percentage": intermediate_result.accuracy * 100,
                                    "total_samples": intermediate_result.total_samples,
                                    "correct_samples": intermediate_result.correct_samples,
                                    "avg_tokens_per_sample": intermediate_result.avg_tokens_per_sample,
                                    "total_tokens": intermediate_result.total_tokens,
                                    "avg_inference_time": intermediate_result.avg_inference_time,
                                    "total_inference_time": intermediate_result.total_inference_time
                                }
                            }
                            
                            with open(progress_file, 'w', encoding='utf-8') as f:
                                json.dump(progress_data, f, indent=2, ensure_ascii=False)
                            
                            logger.info(f"📊 进度已保存: {current_samples}/{total_samples} ({(current_samples/total_samples)*100:.1f}%) - {progress_file}")
                            
                            # 保存中间的路径详细信息
                            if current_samples % 4 == 0 or current_samples == total_samples:  
                                paths_progress_file = save_paths_to_json(
                                    intermediate_result, model_key, f"{dataset_name}_progress_{current_samples}", 
                                    output_dir, progress_timestamp
                                )
                                if paths_progress_file:
                                    logger.info(f"📊 中间路径详细信息已保存: {paths_progress_file}")
                        except Exception as e:
                            logger.warning(f"保存进度时出错: {e}")
                    
                    # 运行评估（传入保存回调）
                    start_time = time.time()
                    results = evaluator.evaluate_dataset(processor, test_data, save_progress_callback=save_progress)
                    evaluation_time = time.time() - start_time
                    
                    # 立即保存详细结果（边运行边保存）
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    # 将模型名称中的路径分隔符替换为安全的字符
                    safe_model_name = model_key.replace("/", "_").replace("\\", "_")
                    detail_file = output_dir / f"vllm_{safe_model_name}_{dataset_name}_{timestamp}.json"
                    save_vllm_results(results, str(detail_file))
                    
                    # 保存路径详细信息到JSON
                    json_file = save_paths_to_json(results, model_key, dataset_name, output_dir, timestamp)
                    if json_file:
                        logger.info(f"📊 路径详细信息已保存到JSON: {json_file}")
                    else:
                        logger.warning(f"⚠️ 无法保存路径详细信息到JSON文件")
                    
                    # 保存GPT验证结果（如果有）
                    if gpt_validator and hasattr(evaluator, 'gpt_validation_results') and evaluator.gpt_validation_results:
                        gpt_results_file = output_dir / f"gpt_validation_{safe_model_name}_{dataset_name}_{timestamp}.json"
                        with open(gpt_results_file, 'w', encoding='utf-8') as f:
                            json.dump({
                                "model_name": model_key,
                                "dataset_name": dataset_name,
                                "gpt_model": args.gpt_model,
                                "validation_sample_rate": args.gpt_validation_sample_rate,
                                "total_validations": len(evaluator.gpt_validation_results),
                                "validations": evaluator.gpt_validation_results
                            }, f, indent=2, ensure_ascii=False)
                        logger.info(f"🤖 GPT验证结果已保存到: {gpt_results_file}")
                    
                    # 清理中间进度文件（除非用户选择保留）
                    if not args.keep_progress_files:
                        cleanup_progress_files(output_dir, safe_model_name, dataset_name)
                    else:
                        logger.info("📁 保留中间进度文件（根据用户设置）")
                    
                    # 保存结果到内存（用于最终汇总）
                    model_results[dataset_name] = {
                        "accuracy": results.accuracy,
                        "accuracy_percentage": results.accuracy * 100,
                        "total_samples": results.total_samples,
                        "correct_samples": results.correct_samples,
                        "avg_tokens_per_sample": results.avg_tokens_per_sample,
                        "total_tokens": results.total_tokens,
                        "avg_prompt_tokens": results.avg_prompt_tokens,
                        "avg_completion_tokens": results.avg_completion_tokens,
                        "avg_inference_time": results.avg_inference_time,
                        "total_inference_time": results.total_inference_time,
                        "evaluation_time": evaluation_time,
                        "tokens_per_second": results.total_tokens / results.total_inference_time if results.total_inference_time > 0 else 0
                    }
                    
                    # 立即保存当前模型的部分结果
                    partial_results = {
                        "timestamp": datetime.now().isoformat(),
                        "evaluation_config": {
                            "models": args.models,
                            "datasets": args.datasets,
                            "few_shot": args.few_shot,
                            "n_consistency_samples": args.n_consistency_samples,
                            "temperature": args.temperature,
                            "top_p": args.top_p,
                            "max_tokens": args.max_tokens,
                            "num_samples": args.num_samples,
                            "quick_test": args.quick_test,
                            "use_cot": args.use_cot,
                            "confidence_temp": args.confidence_temp,
                            "use_path_confidence": args.use_path_confidence,
                            "combined_confidence_threshold": args.combined_confidence_threshold
                        },
                        "current_model": model_key,
                        "current_dataset": dataset_name,
                        "model_config": {
                            "base_url": config.base_url,
                            "model_name": config.model_name
                        },
                        "partial_results": model_results
                    }
                    
                    # 保存部分结果到文件
                    partial_file = output_dir / f"partial_results_{safe_model_name}_{timestamp}.json"
                    with open(partial_file, 'w', encoding='utf-8') as f:
                        json.dump(partial_results, f, indent=2, ensure_ascii=False)
                    logger.info(f"💾 部分结果已保存到: {partial_file}")
                    
                    # 打印摘要
                    logger.info(f"✅ 结果摘要 - {dataset_name}:")
                    logger.info(f"   🎯 准确率: {results.accuracy:.4f} ({results.accuracy*100:.2f}%)")
                    logger.info(f"   📝 平均tokens/问题: {results.avg_tokens_per_sample:.2f}")
                    logger.info(f"   🔢 总tokens: {results.total_tokens:,}")
                    logger.info(f"   ⏱️ 平均推理时间: {results.avg_inference_time:.2f}s")
                    logger.info(f"   🚀 吞吐量: {results.total_tokens/results.total_inference_time:.1f} tokens/s")
                    
                except Exception as e:
                    logger.error(f"❌ 评估数据集 {dataset_name} 时出错: {e}")
                    model_results[dataset_name] = {"error": str(e)}
            
            # 保存模型结果
            all_results["results"][model_key] = {
                "model_config": {
                    "base_url": config.base_url,
                    "model_name": config.model_name
                },
                "results": model_results
            }
            
        except Exception as e:
            logger.error(f"❌ 评估模型 {model_key} 时出错: {e}")
            all_results["results"][model_key] = {"error": str(e)}
    
    # 保存完整结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = output_dir / f"vllm_baseline_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n💾 完整结果已保存到: {results_file}")
    
    # 生成摘要报告
    generate_summary_report(all_results, output_dir / f"vllm_baseline_summary_{timestamp}.txt")
    
    return all_results

def generate_summary_report(results: dict, output_file: Path):
    """生成摘要报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("vLLM Self-Consistency Baseline 评估报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"评估时间: {results['timestamp']}\n")
        f.write(f"评估配置: {results['evaluation_config']}\n\n")
        
        # vLLM服务器配置
        f.write("vLLM服务器配置:\n")
        for model_key, config in results["vllm_configs"].items():
            f.write(f"  - {model_key}: {config['base_url']} ({config['model_name']})\n")
        f.write("\n")
        
        # 各模型结果
        for model_key, model_data in results["results"].items():
            if "error" in model_data:
                f.write(f"模型 {model_key}: 评估失败 - {model_data['error']}\n\n")
                continue
                
            f.write(f"模型: {model_key}\n")
            f.write(f"服务器: {model_data['model_config']['base_url']}\n")
            f.write("-" * 30 + "\n")
            
            for dataset_name, dataset_results in model_data["results"].items():
                if "error" in dataset_results:
                    f.write(f"  {dataset_name}: 评估失败 - {dataset_results['error']}\n")
                    continue
                    
                accuracy = dataset_results["accuracy"]
                avg_tokens = dataset_results["avg_tokens_per_sample"]
                total_tokens = dataset_results["total_tokens"]
                throughput = dataset_results.get("tokens_per_second", 0)
                
                f.write(f"  {dataset_name}:\n")
                f.write(f"    准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n")
                f.write(f"    平均tokens/问题: {avg_tokens:.2f}\n")
                f.write(f"    总tokens: {total_tokens:,}\n")
                f.write(f"    吞吐量: {throughput:.1f} tokens/s\n")
            
            f.write("\n")
    
    logger.info(f"📋 摘要报告已保存到: {output_file}")

if __name__ == "__main__":
    try:
        results = run_vllm_baseline_evaluation()
        logger.info("🎉 vLLM Baseline评估完成！")
    except Exception as e:
        logger.error(f"💥 评估过程中出现错误: {e}")
        sys.exit(1)
