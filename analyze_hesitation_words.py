#!/usr/bin/env python3
"""
分析self-consistency路径中的犹豫词汇脚本
检查response中出现的犹豫词汇，统计它们与confidence和正确性的关系
"""

import json
import re
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import statistics

# 定义犹豫词汇列表
HESITATION_WORDS = [
    # 犹豫和不确定
    "wait", "however", "nevertheless", "think again", "reconsider", "actually", 
    "hmm", "well", "let me think", "hold on", "on second thought", "I think",
    
    # 修正和重新考虑
    "correction", "mistake", "wrong", "error", "revise", "fix", "change my mind",
    "oops", "sorry", "my bad", "let me correct", "I mean", "rather",
    
    # 不确定性表达
    "maybe", "perhaps", "possibly", "might", "could be", "seems like", 
    "appears to", "I guess", "I suppose", "probably", "likely", "uncertain",
    "not sure", "unclear", "confusing", "confused", "unsure",
    
    # 重新开始或回退
    "start over", "back to", "go back", "restart", "re-do", "try again",
    "let me start", "from the beginning", "step back", "recheck", "double check",
    "check again", "verify", "make sure", "let me see",
    
    # 犹豫连接词
    "but then", "on the other hand", "although", "while", "yet", "still",
    "nonetheless", "even so", "that said", "having said that", "but wait",
    
    # 数学推理中的犹豫表达
    "let me recalculate", "wait, that's not right", "let me double-check",
    "actually, let me", "hmm, let me", "wait, I need to", "let me re-examine",
    "hold on, let me", "actually, I think", "wait, I made", "let me verify",
    "I should double-check", "let me make sure", "actually, wait",
    
    # 自我纠正表达
    "no, that's", "wait, that", "actually, that", "correction:", "I meant",
    "let me fix", "that should be", "I need to correct", "my calculation",
    "the correct", "should actually be", "I realize", "upon reflection"
]

def extract_hesitation_words(text: str) -> Dict[str, int]:
    """
    提取文本中的犹豫词汇
    
    Args:
        text: 要分析的文本
        
    Returns:
        字典，键为犹豫词汇，值为出现次数
    """
    text_lower = text.lower()
    hesitation_count = {}
    
    for word in HESITATION_WORDS:
        # 使用正则表达式匹配完整词汇，处理多词短语
        if " " in word:
            # 多词短语，直接搜索
            pattern = re.escape(word.lower())
        else:
            # 单词，使用词边界
            pattern = r'\b' + re.escape(word.lower()) + r'\b'
        
        matches = re.findall(pattern, text_lower)
        if matches:
            hesitation_count[word] = len(matches)
    
    return hesitation_count

def calculate_hesitation_score(hesitation_words: Dict[str, int]) -> float:
    """
    计算犹豫分数
    
    Args:
        hesitation_words: 犹豫词汇统计
        
    Returns:
        犹豫分数（总出现次数）
    """
    return sum(hesitation_words.values())

def analyze_paths_file(file_path: Path) -> Dict[str, Any]:
    """
    分析单个paths文件中的犹豫词汇
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        分析结果
    """
    print(f"📖 分析文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 初始化统计数据
    analysis_results = {
        "file_info": {
            "file_path": str(file_path),
            "total_questions": 0,
            "total_paths": 0
        },
        "hesitation_analysis": {
            "questions_with_hesitation": 0,
            "paths_with_hesitation": 0,
            "total_hesitation_words": 0,
            "word_frequency": defaultdict(int),
            "hesitation_vs_confidence": [],
            "hesitation_vs_correctness": [],
            "detailed_stats": {
                "correct_with_hesitation": 0,
                "correct_without_hesitation": 0,
                "incorrect_with_hesitation": 0,
                "incorrect_without_hesitation": 0
            }
        },
        "question_details": []
    }
    
    # 判断JSON格式
    if "questions" in data:
        # 新格式
        questions_data = data["questions"]
    else:
        # 旧格式，直接是问题列表
        questions_data = data
    
    analysis_results["file_info"]["total_questions"] = len(questions_data)
    
    for q_idx, question_data in enumerate(questions_data):
        question_detail = {
            "question_index": q_idx,
            "question": question_data.get("question", ""),
            "gold_answer": question_data.get("gold_answer", ""),
            "final_answer": question_data.get("final_answer", ""),
            "is_correct": question_data.get("is_correct", False),
            "confidence": question_data.get("confidence", 0.0),
            "paths": []
        }
        
        question_has_hesitation = False
        
        # 分析每个路径
        for path_idx, path in enumerate(question_data.get("paths", [])):
            # 尝试获取不同可能的文本字段
            reasoning = path.get("reasoning", "") or path.get("reasoning_path", "")
            answer = path.get("answer", "") or path.get("extracted_answer", "")
            response_text = path.get("response_text", "")
            
            # 使用最完整的文本进行分析
            if response_text:
                full_response = response_text
            else:
                full_response = f"{reasoning} {answer}"
            
            # 提取犹豫词汇
            hesitation_words = extract_hesitation_words(full_response)
            hesitation_score = calculate_hesitation_score(hesitation_words)
            
            path_detail = {
                "path_index": path_idx,
                "reasoning": reasoning,
                "answer": answer,
                "path_confidence": path.get("confidence", 0.0),
                "hesitation_words": hesitation_words,
                "hesitation_score": hesitation_score,
                "has_hesitation": hesitation_score > 0
            }
            
            question_detail["paths"].append(path_detail)
            analysis_results["file_info"]["total_paths"] += 1
            
            # 更新统计
            if hesitation_score > 0:
                analysis_results["hesitation_analysis"]["paths_with_hesitation"] += 1
                question_has_hesitation = True
                
                # 更新词频统计
                for word, count in hesitation_words.items():
                    analysis_results["hesitation_analysis"]["word_frequency"][word] += count
                
                analysis_results["hesitation_analysis"]["total_hesitation_words"] += hesitation_score
            
            # 记录犹豫与置信度关系
            analysis_results["hesitation_analysis"]["hesitation_vs_confidence"].append({
                "hesitation_score": hesitation_score,
                "confidence": path.get("confidence", 0.0),
                "has_hesitation": hesitation_score > 0
            })
        
        # 更新问题级别统计
        if question_has_hesitation:
            analysis_results["hesitation_analysis"]["questions_with_hesitation"] += 1
        
        # 统计正确性与犹豫关系
        is_correct = question_data.get("is_correct", False)
        question_hesitation_score = sum(path["hesitation_score"] for path in question_detail["paths"])
        
        analysis_results["hesitation_analysis"]["hesitation_vs_correctness"].append({
            "question_index": q_idx,
            "is_correct": is_correct,
            "question_hesitation_score": question_hesitation_score,
            "has_hesitation": question_hesitation_score > 0,
            "confidence": question_data.get("confidence", 0.0)
        })
        
        # 更新详细统计
        if is_correct:
            if question_hesitation_score > 0:
                analysis_results["hesitation_analysis"]["detailed_stats"]["correct_with_hesitation"] += 1
            else:
                analysis_results["hesitation_analysis"]["detailed_stats"]["correct_without_hesitation"] += 1
        else:
            if question_hesitation_score > 0:
                analysis_results["hesitation_analysis"]["detailed_stats"]["incorrect_with_hesitation"] += 1
            else:
                analysis_results["hesitation_analysis"]["detailed_stats"]["incorrect_without_hesitation"] += 1
        
        question_detail["question_hesitation_score"] = question_hesitation_score
        analysis_results["question_details"].append(question_detail)
    
    return analysis_results

def generate_summary_statistics(analysis_results: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成汇总统计信息
    
    Args:
        analysis_results: 分析结果
        
    Returns:
        汇总统计
    """
    hesitation_data = analysis_results["hesitation_analysis"]
    
    # 计算置信度统计
    confidence_with_hesitation = []
    confidence_without_hesitation = []
    
    for item in hesitation_data["hesitation_vs_confidence"]:
        if item["has_hesitation"]:
            confidence_with_hesitation.append(item["confidence"])
        else:
            confidence_without_hesitation.append(item["confidence"])
    
    # 计算正确率统计
    stats = hesitation_data["detailed_stats"]
    total_correct = stats["correct_with_hesitation"] + stats["correct_without_hesitation"]
    total_incorrect = stats["incorrect_with_hesitation"] + stats["incorrect_without_hesitation"]
    total_questions = total_correct + total_incorrect
    
    correct_with_hesitation_rate = stats["correct_with_hesitation"] / total_questions if total_questions > 0 else 0
    correct_without_hesitation_rate = stats["correct_without_hesitation"] / total_questions if total_questions > 0 else 0
    
    # 计算犹豫问题的正确率
    total_with_hesitation = stats["correct_with_hesitation"] + stats["incorrect_with_hesitation"]
    total_without_hesitation = stats["correct_without_hesitation"] + stats["incorrect_without_hesitation"]
    
    accuracy_with_hesitation = stats["correct_with_hesitation"] / total_with_hesitation if total_with_hesitation > 0 else 0
    accuracy_without_hesitation = stats["correct_without_hesitation"] / total_without_hesitation if total_without_hesitation > 0 else 0
    
    summary = {
        "overall_statistics": {
            "total_questions": analysis_results["file_info"]["total_questions"],
            "total_paths": analysis_results["file_info"]["total_paths"],
            "questions_with_hesitation_percentage": (hesitation_data["questions_with_hesitation"] / analysis_results["file_info"]["total_questions"]) * 100 if analysis_results["file_info"]["total_questions"] > 0 else 0,
            "paths_with_hesitation_percentage": (hesitation_data["paths_with_hesitation"] / analysis_results["file_info"]["total_paths"]) * 100 if analysis_results["file_info"]["total_paths"] > 0 else 0,
            "average_hesitation_words_per_question": hesitation_data["total_hesitation_words"] / analysis_results["file_info"]["total_questions"] if analysis_results["file_info"]["total_questions"] > 0 else 0
        },
        "confidence_statistics": {
            "avg_confidence_with_hesitation": statistics.mean(confidence_with_hesitation) if confidence_with_hesitation else 0,
            "avg_confidence_without_hesitation": statistics.mean(confidence_without_hesitation) if confidence_without_hesitation else 0,
            "confidence_samples_with_hesitation": len(confidence_with_hesitation),
            "confidence_samples_without_hesitation": len(confidence_without_hesitation)
        },
        "correctness_statistics": {
            "accuracy_with_hesitation": accuracy_with_hesitation,
            "accuracy_without_hesitation": accuracy_without_hesitation,
            "correct_with_hesitation_rate": correct_with_hesitation_rate,
            "correct_without_hesitation_rate": correct_without_hesitation_rate,
            "questions_with_hesitation": total_with_hesitation,
            "questions_without_hesitation": total_without_hesitation
        },
        "top_hesitation_words": dict(sorted(hesitation_data["word_frequency"].items(), key=lambda x: x[1], reverse=True)[:10])
    }
    
    return summary

def main():
    parser = argparse.ArgumentParser(description="分析self-consistency路径中的犹豫词汇")
    parser.add_argument("input_file", help="输入的JSON文件路径")
    parser.add_argument("--output_dir", default="hesitation_analysis", help="输出目录")
    parser.add_argument("--output_prefix", default="hesitation_analysis", help="输出文件前缀")
    
    args = parser.parse_args()
    
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"❌ 输入文件不存在: {input_path}")
        return
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    print(f"🔍 开始分析犹豫词汇...")
    print(f"📁 输入文件: {input_path}")
    print(f"📁 输出目录: {output_dir}")
    
    # 分析文件
    analysis_results = analyze_paths_file(input_path)
    
    # 生成汇总统计
    summary_stats = generate_summary_statistics(analysis_results)
    
    # 保存详细分析结果
    detailed_output_file = output_dir / f"{args.output_prefix}_detailed.json"
    with open(detailed_output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    print(f"💾 详细分析结果已保存: {detailed_output_file}")
    
    # 保存汇总统计
    summary_output_file = output_dir / f"{args.output_prefix}_summary.json"
    with open(summary_output_file, 'w', encoding='utf-8') as f:
        json.dump(summary_stats, f, indent=2, ensure_ascii=False)
    print(f"💾 汇总统计已保存: {summary_output_file}")
    
    # 打印关键统计信息
    print(f"\n📊 分析结果摘要:")
    print(f"总问题数: {summary_stats['overall_statistics']['total_questions']}")
    print(f"总路径数: {summary_stats['overall_statistics']['total_paths']}")
    print(f"包含犹豫词汇的问题比例: {summary_stats['overall_statistics']['questions_with_hesitation_percentage']:.1f}%")
    print(f"包含犹豫词汇的路径比例: {summary_stats['overall_statistics']['paths_with_hesitation_percentage']:.1f}%")
    
    print(f"\n🎯 正确率对比:")
    print(f"有犹豫词汇的问题正确率: {summary_stats['correctness_statistics']['accuracy_with_hesitation']:.3f}")
    print(f"无犹豫词汇的问题正确率: {summary_stats['correctness_statistics']['accuracy_without_hesitation']:.3f}")
    
    print(f"\n💡 置信度对比:")
    print(f"有犹豫词汇的平均置信度: {summary_stats['confidence_statistics']['avg_confidence_with_hesitation']:.3f}")
    print(f"无犹豫词汇的平均置信度: {summary_stats['confidence_statistics']['avg_confidence_without_hesitation']:.3f}")
    
    print(f"\n🔤 最常见的犹豫词汇:")
    for word, count in list(summary_stats['top_hesitation_words'].items())[:5]:
        print(f"  {word}: {count} 次")
    
    print(f"\n✅ 分析完成！")

if __name__ == "__main__":
    main()
