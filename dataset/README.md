# 本地数据集说明

## 下载日期
2025-07-30 03:11:52

## 数据集列表

### 1. MMLU (大规模多任务语言理解)
- 包含57个学科的4选择题
- 文件位置: `mmlu/`
- 格式: CSV, JSON

### 2. MMLU-Pro (MMLU增强版)
- 更难的多项选择题，选项更多
- 文件位置: `mmlu-pro/`
- 格式: CSV, JSON

### 3. GSM8K (小学数学推理)
- 小学数学应用题，需要多步推理
- 文件位置: `gsm8k/`
- 格式: CSV, JSON

## 使用方法

### 在评估系统中使用本地数据集
修改 `src/datasets.py` 中的数据集加载路径，从本地文件加载数据。

### 直接加载CSV格式
```python
import pandas as pd

# 加载MMLU测试集
mmlu_test = pd.read_csv('dataset/mmlu/mmlu_test.csv')
```

### 直接加载JSON格式
```python
import json

# 加载GSM8K训练集
with open('dataset/gsm8k/gsm8k_train.json', 'r') as f:
    gsm8k_train = json.load(f)
```

## 总计
- 总样本数: 136,594
- 数据集数: 3
- 支持格式: csv, json

## 文件结构
```
dataset/
├── mmlu/
│   ├── mmlu_test.csv
│   ├── mmlu_test.json
│   └── dataset_info.json
├── mmlu-pro/
│   ├── mmlu_pro_test.csv
│   ├── mmlu_pro_test.json
│   └── dataset_info.json
├── gsm8k/
│   ├── gsm8k_train.csv
│   ├── gsm8k_train.json
│   ├── gsm8k_test.csv
│   ├── gsm8k_test.json
│   └── dataset_info.json
├── datasets_summary.json
└── README.md
```
