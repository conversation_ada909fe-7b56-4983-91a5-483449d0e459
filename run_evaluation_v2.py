import os
import argparse
import json
from datetime import datetime
from typing import Dict, Any, List

from src.models import get_model, InferenceConfig
from src.datasets import get_dataset_processor, sample_dataset
from src.evaluator import SelfConsistencyEvaluator, save_results
from src.analysis import create_summary_report, plot_results
from src.config import get_config_manager

import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('evaluation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_evaluation_with_config(
    models: List[str],
    datasets: List[str], 
    inference_config_name: str = "default",
    evaluation_setting: str = "full_evaluation",
    output_dir: str = "results"
):
    """使用配置文件运行评估"""
    
    config_manager = get_config_manager()
    
    # 获取配置
    inference_config_dict = config_manager.get_inference_config(inference_config_name)
    eval_settings = config_manager.get_evaluation_settings(evaluation_setting)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_dir, f"evaluation_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存配置
    config = {
        "models": models,
        "datasets": datasets,
        "inference_config_name": inference_config_name,
        "inference_config": inference_config_dict,
        "evaluation_setting": evaluation_setting,
        "evaluation_settings": eval_settings.__dict__,
        "timestamp": timestamp
    }
    
    with open(os.path.join(output_dir, "config.json"), "w") as f:
        json.dump(config, f, indent=2)
    
    # 创建推理配置
    inference_config = InferenceConfig(
        num_samples=inference_config_dict["num_samples"],
        temperature=inference_config_dict["temperature"],
        top_p=inference_config_dict["top_p"],
        max_new_tokens=inference_config_dict["max_new_tokens"]
    )
    
    logger.info(f"开始评估 - 配置: {inference_config_name}, 设置: {evaluation_setting}")
    logger.info(f"推理参数: samples={inference_config.num_samples}, "
               f"temp={inference_config.temperature}, top_p={inference_config.top_p}")
    
    all_results = {}
    
    # 评估每个模型在每个数据集上的表现
    for model_name in models:
        logger.info(f"开始评估模型: {model_name}")
        
        try:
            # 获取模型配置信息
            model_config = config_manager.get_model_config(model_name)
            logger.info(f"模型描述: {model_config['description']}")
            
            # 加载模型
            model = get_model(model_name)
            evaluator = SelfConsistencyEvaluator(model, inference_config)
            
            model_results = {}
            
            for dataset_name in datasets:
                logger.info(f"评估 {model_name} 在 {dataset_name} 上的表现")
                
                try:
                    # 获取数据集配置信息
                    dataset_config = config_manager.get_dataset_config(dataset_name)
                    logger.info(f"数据集描述: {dataset_config['description']}")
                    
                    # 获取预期性能（如果有）
                    expected_perf = model_config.get("expected_performance", {}).get(dataset_name, "未知")
                    logger.info(f"预期性能范围: {expected_perf}")
                    
                    # 加载数据集
                    processor = get_dataset_processor(dataset_name)
                    
                    # 运行评估  
                    num_samples = eval_settings.num_samples_per_dataset
                    results = evaluator.evaluate_dataset(processor, num_samples)
                    
                    # 保存单个结果
                    result_file = os.path.join(output_dir, f"{model_name}_{dataset_name}_results.json")
                    save_results(results, result_file)
                    
                    model_results[dataset_name] = results
                    
                    logger.info(f"完成 {model_name} 在 {dataset_name}: "
                               f"准确率 = {results.accuracy:.4f}, "
                               f"平均Token数 = {results.avg_tokens_per_sample:.1f}")
                    
                except Exception as e:
                    logger.error(f"评估 {model_name} 在 {dataset_name} 时出错: {e}")
                    continue
            
            all_results[model_name] = model_results
            
        except Exception as e:
            logger.error(f"加载模型 {model_name} 时出错: {e}")
            continue
    
    # 生成汇总报告
    if all_results:
        logger.info("生成汇总报告...")
        
        try:
            # 创建汇总报告
            summary_file = os.path.join(output_dir, "summary_report.md")
            create_summary_report(all_results, summary_file)
            
            # 创建可视化图表
            plot_file = os.path.join(output_dir, "results_visualization.png")
            plot_results(all_results, plot_file)
            
            logger.info(f"评估完成。结果保存在 {output_dir}")
            
            # 打印简要结果
            print("\n" + "="*60)
            print("Self-Consistency Baseline 评估结果")
            print("="*60)
            
            for model_name, model_results in all_results.items():
                print(f"\n{model_name.upper()}:")
                for dataset_name, results in model_results.items():
                    print(f"  {dataset_name.upper()}: "
                          f"准确率={results.accuracy:.4f}, "
                          f"Token数={results.avg_tokens_per_sample:.1f}")
            
            print(f"\n详细结果保存在: {output_dir}")
            print("="*60)
            
        except Exception as e:
            logger.error(f"生成汇总报告时出错: {e}")
    
    else:
        logger.error("没有成功完成的评估")
    
    return all_results

def main():
    parser = argparse.ArgumentParser(description="Self-Consistency Baseline Evaluation with Config")
    
    parser.add_argument("--models", nargs="+", 
                       help="要评估的模型 (如果未指定，使用配置文件中的所有模型)")
    
    parser.add_argument("--datasets", nargs="+",
                       help="要评估的数据集 (如果未指定，使用配置文件中的所有数据集)")
    
    parser.add_argument("--inference_config", type=str, default="default",
                       help="推理配置名称 (default, conservative, diverse)")
    
    parser.add_argument("--evaluation_setting", type=str, default="full_evaluation",
                       help="评估设置 (quick_test, development, full_evaluation)")
    
    parser.add_argument("--output_dir", type=str, default="results",
                       help="输出目录")
    
    parser.add_argument("--show_config", action="store_true",
                       help="显示配置信息并退出")
    
    args = parser.parse_args()
    
    # 获取配置管理器
    config_manager = get_config_manager()
    
    if args.show_config:
        config_manager.print_config_summary()
        return
    
    # 确定要使用的模型和数据集
    models = args.models if args.models else config_manager.get_available_models()
    datasets = args.datasets if args.datasets else config_manager.get_available_datasets()
    
    print(f"将评估以下配置:")
    print(f"- 模型: {models}")
    print(f"- 数据集: {datasets}")
    print(f"- 推理配置: {args.inference_config}")
    print(f"- 评估设置: {args.evaluation_setting}")
    print()
    
    # 运行评估
    run_evaluation_with_config(
        models=models,
        datasets=datasets,
        inference_config_name=args.inference_config,
        evaluation_setting=args.evaluation_setting,
        output_dir=args.output_dir
    )

if __name__ == "__main__":
    main()
