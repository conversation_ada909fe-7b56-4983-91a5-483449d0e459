#!/usr/bin/env python3
"""
使用本地数据集运行完整的baseline评估
"""

import os
import sys
import json
import logging
import time
import torch
from pathlib import Path
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.local_datasets import get_local_dataset_processor, sample_local_dataset
from src.models import ModelWrapper, ModelConfig, InferenceConfig
from src.evaluator import SelfConsistencyEvaluator
from src.gpu_utils import setup_gpu_environment

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_local_baseline_evaluation():
    """使用本地数据集运行baseline评估"""
    
    # GPU环境设置
    logger.info("设置GPU环境...")
    setup_gpu_environment()
    
    # 获取GPU信息
    gpu_info = []
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            gpu_info.append({
                "id": i,
                "name": torch.cuda.get_device_name(i),
                "memory_total": torch.cuda.get_device_properties(i).total_memory // 1024**2  # MB
            })
    logger.info(f"GPU信息: {gpu_info}")
    
    # 配置
    models_config = {
        "ministral-8b": {
            "path": "/data/ministral-7b",
            "name": "Ministral-8B-Instruct-2410"
        },
        "gemma-2-2b": {
            "path": "/data/gemma-2-2b", 
            "name": "GEMMA-2-2B"
        },
        "qwen2.5-3b": {
            "path": "/data/qwen2.5-3b",
            "name": "Qwen2.5-3B"
        }
    }
    
    datasets = ["MMLU", "MMLU-Pro", "GSM8K"]
    
    # Self-consistency参数
    sc_params = {
        "n_samples": 5,
        "temperature": 0.7,
        "top_p": 0.9
    }
    
    # 测试样本数量（如果需要快速测试，可以减少样本数）
    sample_sizes = {
        "MMLU": 500,       # 从14042个中采样500个
        "MMLU-Pro": 200,   # 全部测试样本
        "GSM8K": 300       # 从1319个中采样300个
    }
    
    # 结果存储
    all_results = {
        "timestamp": datetime.now().isoformat(),
        "gpu_info": gpu_info,
        "self_consistency_params": sc_params,
        "sample_sizes": sample_sizes,
        "results": {}
    }
    
    # 遍历每个模型和数据集
    for model_key, model_config in models_config.items():
        logger.info(f"\n开始评估模型: {model_config['name']}")
        
        # 检查模型路径是否存在
        if not os.path.exists(model_config["path"]):
            logger.warning(f"模型路径不存在，跳过: {model_config['path']}")
            continue
        
        try:
            # 创建模型配置
            config = ModelConfig(
                name=model_key,
                model_name=model_config["path"]
            )
            
            # 加载模型
            logger.info(f"加载模型: {model_config['path']}")
            model = ModelWrapper(config)
            
            # 创建推理配置
            inference_config = InferenceConfig(
                num_samples=sc_params["n_samples"],
                temperature=sc_params["temperature"],
                top_p=sc_params["top_p"],
                max_new_tokens=512
            )
            
            model_results = {}
            
            # 遍历每个数据集
            for dataset_name in datasets:
                logger.info(f"\n评估数据集: {dataset_name}")
                
                try:
                    # 加载本地数据集
                    processor = get_local_dataset_processor(dataset_name)
                    
                    # 采样数据
                    sample_size = sample_sizes.get(dataset_name)
                    test_data = sample_local_dataset(processor, sample_size)
                    
                    logger.info(f"使用 {len(test_data)} 个样本进行评估")
                    
                    # 创建评估器
                    evaluator = SelfConsistencyEvaluator(
                        model=model,
                        inference_config=inference_config
                    )
                    
                    # 运行评估
                    results = evaluator.evaluate_dataset(processor, len(test_data))
                    
                    # 保存结果
                    model_results[dataset_name] = {
                        "accuracy": results.accuracy,
                        "total_samples": results.total_samples,
                        "correct_samples": results.correct_samples,
                        "average_tokens_per_question": results.avg_tokens_per_sample,
                        "total_tokens": results.total_tokens,
                        "average_inference_time": results.avg_inference_time,
                        "total_inference_time": results.total_inference_time
                    }
                    
                    # 打印摘要
                    accuracy = results.accuracy
                    avg_tokens = results.avg_tokens_per_sample
                    total_tokens = results.total_tokens
                    
                    logger.info(f"结果摘要 - {dataset_name}:")
                    logger.info(f"  准确率: {accuracy:.4f}")
                    logger.info(f"  平均tokens/问题: {avg_tokens:.2f}")
                    logger.info(f"  总tokens: {total_tokens}")
                    
                except Exception as e:
                    logger.error(f"评估数据集 {dataset_name} 时出错: {e}")
                    model_results[dataset_name] = {"error": str(e)}
            
            # 保存模型结果
            all_results["results"][model_key] = {
                "model_name": model_config["name"],
                "model_path": model_config["path"],
                "results": model_results
            }
            
            # 清理模型内存
            del model
            
        except Exception as e:
            logger.error(f"评估模型 {model_config['name']} 时出错: {e}")
            all_results["results"][model_key] = {"error": str(e)}
    
    # 保存完整结果
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = results_dir / f"baseline_results_local_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n完整结果已保存到: {results_file}")
    
    # 生成摘要报告
    generate_summary_report(all_results, results_dir / f"baseline_summary_local_{timestamp}.txt")
    
    return all_results

def generate_summary_report(results: dict, output_file: Path):
    """生成摘要报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Self-Consistency Baseline 评估报告（本地数据集）\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"评估时间: {results['timestamp']}\n")
        f.write(f"Self-Consistency参数: {results['self_consistency_params']}\n")
        f.write(f"样本数量: {results['sample_sizes']}\n\n")
        
        # GPU信息
        if "gpu_info" in results:
            f.write("GPU信息:\n")
            for gpu in results["gpu_info"]:
                f.write(f"  - {gpu['name']}: {gpu['memory_total']}MB 显存\n")
        f.write("\n")
        
        # 各模型结果
        for model_key, model_data in results["results"].items():
            if "error" in model_data:
                f.write(f"模型 {model_key}: 评估失败 - {model_data['error']}\n\n")
                continue
                
            f.write(f"模型: {model_data['model_name']}\n")
            f.write("-" * 30 + "\n")
            
            for dataset_name, dataset_results in model_data["results"].items():
                if "error" in dataset_results:
                    f.write(f"  {dataset_name}: 评估失败 - {dataset_results['error']}\n")
                    continue
                    
                accuracy = dataset_results["accuracy"]
                avg_tokens = dataset_results["average_tokens_per_question"]
                total_tokens = dataset_results["total_tokens"]
                
                f.write(f"  {dataset_name}:\n")
                f.write(f"    准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n")
                f.write(f"    平均tokens/问题: {avg_tokens:.2f}\n")
                f.write(f"    总tokens: {total_tokens:,}\n")
            
            f.write("\n")
    
    logger.info(f"摘要报告已保存到: {output_file}")

if __name__ == "__main__":
    try:
        results = run_local_baseline_evaluation()
        logger.info("Baseline评估完成！")
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        sys.exit(1)
