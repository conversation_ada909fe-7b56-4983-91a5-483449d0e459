#!/usr/bin/env python3
"""
分析路径详细信息JSON文件的辅助脚本
"""

import json
import argparse
from pathlib import Path
import pandas as pd

def analyze_paths_json(json_file: str):
    """分析路径详细信息JSON文件"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 分析文件: {json_file}")
    print("=" * 50)
    
def analyze_paths_json(json_file: str):
    """分析路径详细信息JSON文件"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 分析文件: {json_file}")
    print("=" * 50)
    
    # 检测数据格式并适配
    if 'metadata' in data and 'questions' in data:
        # 新格式
        analyze_new_format(data)
    elif 'model_results' in data:
        # 旧格式
        analyze_old_format(data)
    else:
        print("❌ 无法识别的JSON文件格式")
        return

def analyze_new_format(data):
    """分析新格式的JSON数据"""
    # 元数据信息
    metadata = data['metadata']
    print(f"模型名称: {metadata['model_name']}")
    print(f"数据集: {metadata['dataset_name']}")
    print(f"时间戳: {metadata['timestamp']}")
    print(f"总问题数: {metadata['total_questions']}")
    print(f"总路径数: {metadata['total_paths']}")
    print(f"平均每题路径数: {metadata['total_paths'] / metadata['total_questions']:.1f}")
    print()
    
    # 统计准确率
    total_questions = len(data['questions'])
    correct_questions = sum(1 for q in data['questions'] if q['is_correct'])
    accuracy = correct_questions / total_questions if total_questions > 0 else 0
    print(f"🎯 准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"正确回答: {correct_questions}/{total_questions}")
    print()
    
    # 置信度统计
    all_answer_conf = []
    all_path_conf = []
    all_combined_conf = []
    
    for question in data['questions']:
        for path in question['paths']:
            all_answer_conf.append(path['answer_confidence'])
            all_path_conf.append(path['path_confidence'])
            all_combined_conf.append(path['combined_confidence'])
    
    if all_answer_conf:  # 确保有数据
        print("📈 置信度统计:")
        print(f"答案置信度 - 平均: {sum(all_answer_conf)/len(all_answer_conf):.3f}, "
              f"最小: {min(all_answer_conf):.3f}, 最大: {max(all_answer_conf):.3f}")
        print(f"路径置信度 - 平均: {sum(all_path_conf)/len(all_path_conf):.3f}, "
              f"最小: {min(all_path_conf):.3f}, 最大: {max(all_path_conf):.3f}")
        print(f"组合置信度 - 平均: {sum(all_combined_conf)/len(all_combined_conf):.3f}, "
              f"最小: {min(all_combined_conf):.3f}, 最大: {max(all_combined_conf):.3f}")
        print()
    
    # 错误案例分析
    wrong_questions = [q for q in data['questions'] if not q['is_correct']]
    if wrong_questions:
        print(f"❌ 错误案例分析 ({len(wrong_questions)}个):")
        for i, q in enumerate(wrong_questions[:5]):  # 只显示前5个
            print(f"  {i+1}. 问题ID {q['question_id']}: 预测={q['final_answer']}, 正确={q['gold_answer']}")
            if q['paths']:
                avg_conf = sum(p['combined_confidence'] for p in q['paths']) / len(q['paths'])
                print(f"     平均组合置信度: {avg_conf:.3f}")
        if len(wrong_questions) > 5:
            print(f"     ... 还有 {len(wrong_questions) - 5} 个错误案例")
    else:
        print("✅ 所有问题都回答正确！")

def analyze_old_format(data):
    """分析旧格式的JSON数据"""
    model_results = data['model_results']
    
    # 基本信息
    print(f"模型名称: {model_results['model_name']}")
    print(f"数据集: {model_results['dataset_name']}")
    print(f"总问题数: {model_results['total_samples']}")
    print(f"总路径数: {sum(len(sr['responses']) for sr in model_results['sample_results'])}")
    avg_paths_per_question = sum(len(sr['responses']) for sr in model_results['sample_results']) / model_results['total_samples']
    print(f"平均每题路径数: {avg_paths_per_question:.1f}")
    print()
    
    # 统计准确率
    accuracy = model_results['accuracy']
    correct_samples = model_results['correct_samples']
    total_samples = model_results['total_samples']
    print(f"🎯 准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"正确回答: {correct_samples}/{total_samples}")
    print()
    
    # 置信度统计（如果有的话）
    all_answer_conf = []
    all_path_conf = []
    all_combined_conf = []
    
    for sample_result in model_results['sample_results']:
        if 'answer_confidence_scores' in sample_result:
            all_answer_conf.extend(sample_result['answer_confidence_scores'])
        if 'path_confidence_scores' in sample_result:
            all_path_conf.extend(sample_result['path_confidence_scores'])
        if 'combined_confidence_scores' in sample_result:
            all_combined_conf.extend(sample_result['combined_confidence_scores'])
    
    if all_answer_conf:  # 只有当有置信度数据时才显示
        print("📈 置信度统计:")
        print(f"答案置信度 - 平均: {sum(all_answer_conf)/len(all_answer_conf):.3f}, "
              f"最小: {min(all_answer_conf):.3f}, 最大: {max(all_answer_conf):.3f}")
        if all_path_conf:
            print(f"路径置信度 - 平均: {sum(all_path_conf)/len(all_path_conf):.3f}, "
                  f"最小: {min(all_path_conf):.3f}, 最大: {max(all_path_conf):.3f}")
        if all_combined_conf:
            print(f"组合置信度 - 平均: {sum(all_combined_conf)/len(all_combined_conf):.3f}, "
                  f"最小: {min(all_combined_conf):.3f}, 最大: {max(all_combined_conf):.3f}")
        print()
    
    # 错误案例分析
    wrong_samples = [sr for sr in model_results['sample_results'] if not sr['is_correct']]
    if wrong_samples:
        print(f"❌ 错误案例分析 ({len(wrong_samples)}个):")
        for i, sr in enumerate(wrong_samples[:5]):  # 只显示前5个
            print(f"  {i+1}. 问题ID {sr['question_id']}: 预测={sr['final_answer']}, 正确={sr['gold_answer']}")
            if 'combined_confidence_scores' in sr and sr['combined_confidence_scores']:
                avg_conf = sum(sr['combined_confidence_scores']) / len(sr['combined_confidence_scores'])
                print(f"     平均组合置信度: {avg_conf:.3f}")
        if len(wrong_samples) > 5:
            print(f"     ... 还有 {len(wrong_samples) - 5} 个错误案例")
    else:
        print("✅ 所有问题都回答正确！")

def convert_to_csv(json_file: str, output_csv: str = None):
    """将JSON格式转换为扁平的CSV格式（如果需要）"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 扁平化数据
    rows = []
    
    if 'metadata' in data and 'questions' in data:
        # 新格式
        for question in data['questions']:
            for path in question['paths']:
                row = {
                    'question_id': question['question_id'],
                    'question': question['question'],
                    'gold_answer': question['gold_answer'],
                    'final_answer': question['final_answer'],
                    'is_correct': question['is_correct'],
                    'path_index': path['path_index'],
                    'extracted_answer': path['extracted_answer'],
                    'answer_confidence': path['answer_confidence'],
                    'path_confidence': path['path_confidence'],
                    'combined_confidence': path['combined_confidence'],
                    'reasoning_path': path['reasoning_path'],
                    'answer_part': path['answer_part']
                }
                rows.append(row)
    elif 'model_results' in data:
        # 旧格式
        model_results = data['model_results']
        for sample_result in model_results['sample_results']:
            for i, response in enumerate(sample_result['responses']):
                # 分离推理路径和答案部分
                reasoning_path, answer_part = extract_reasoning_and_answer_simple(response)
                
                row = {
                    'question_id': sample_result['question_id'],
                    'question': sample_result['question'],
                    'gold_answer': sample_result['gold_answer'],
                    'final_answer': sample_result['final_answer'],
                    'is_correct': sample_result['is_correct'],
                    'path_index': i,
                    'response_text': response,
                    'extracted_answer': sample_result['extracted_answers'][i] if i < len(sample_result['extracted_answers']) else "",
                    'answer_confidence': sample_result['answer_confidence_scores'][i] if 'answer_confidence_scores' in sample_result and i < len(sample_result['answer_confidence_scores']) else 0.0,
                    'path_confidence': sample_result['path_confidence_scores'][i] if 'path_confidence_scores' in sample_result and i < len(sample_result['path_confidence_scores']) else 0.0,
                    'combined_confidence': sample_result['combined_confidence_scores'][i] if 'combined_confidence_scores' in sample_result and i < len(sample_result['combined_confidence_scores']) else 0.0,
                    'reasoning_path': reasoning_path,
                    'answer_part': answer_part
                }
                rows.append(row)
    
    # 保存为CSV
    if output_csv is None:
        output_csv = json_file.replace('.json', '_flattened.csv')
    
    df = pd.DataFrame(rows)
    df.to_csv(output_csv, index=False, encoding='utf-8')
    print(f"📄 已转换为CSV: {output_csv}")

def extract_reasoning_and_answer_simple(response: str):
    """简单的推理路径和答案分离函数"""
    import re
    match = re.search(r'The final answer is', response, re.IGNORECASE)
    if match:
        split_point = match.start()
        reasoning_path = response[:split_point].strip()
        answer_part = response[split_point:].strip()
        return reasoning_path, answer_part
    else:
        return response.strip(), ""

def main():
    parser = argparse.ArgumentParser(description="分析路径详细信息JSON文件")
    parser.add_argument("json_file", help="JSON文件路径")
    parser.add_argument("--to-csv", action="store_true", help="转换为CSV格式")
    parser.add_argument("--output-csv", help="输出CSV文件名")
    
    args = parser.parse_args()
    
    if not Path(args.json_file).exists():
        print(f"❌ 文件不存在: {args.json_file}")
        return
    
    # 分析JSON文件
    analyze_paths_json(args.json_file)
    
    # 如果需要，转换为CSV
    if args.to_csv:
        try:
            convert_to_csv(args.json_file, args.output_csv)
        except ImportError:
            print("⚠️ 需要安装pandas库来转换CSV: pip install pandas")

if __name__ == "__main__":
    main()
