#!/bin/bash

# GSM8K多角色对比评估脚本
# 对每道题依次使用四种prompt模板进行评估，分析不同角色的表现差异
# 
# 使用方法:
#   ./scripts/role_run/run_gsm8k_all_role.sh [选项]

echo "🎭 开始GSM8K多角色对比评估..."

# 创建结果和日志目录
mkdir -p results/role_comparison
mkdir -p logs/role_comparison

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 默认配置
MODEL="Mistral-7B-Instruct-v0.3"
BASE_URL="http://localhost:2222"
NUM_SAMPLES=50  # 默认50道题，可以调整
FEW_SHOT=3
N_CONSISTENCY_SAMPLES=1
TEMPERATURE=0.2
TOP_P=0.9
MAX_TOKENS=512
CONFIDENCE_TEMP=0.2
OUTPUT_DIR="results/role_comparison"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --model)
            MODEL="$2"
            shift 2
            ;;
        --base_url)
            BASE_URL="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            BASE_URL="http://localhost:$PORT"
            shift 2
            ;;
        --num_samples)
            NUM_SAMPLES="$2"
            shift 2
            ;;
        --few_shot)
            FEW_SHOT="$2"
            shift 2
            ;;
        --consistency)
            N_CONSISTENCY_SAMPLES="$2"
            shift 2
            ;;
        --quick)
            NUM_SAMPLES=20
            echo "🏃 快速测试模式：使用 $NUM_SAMPLES 个样本"
            shift
            ;;
        --full)
            NUM_SAMPLES=100
            echo "📊 完整评估模式：使用 $NUM_SAMPLES 个样本"
            shift
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help|-h)
            echo "GSM8K多角色对比评估脚本"
            echo ""
            echo "使用方法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --model MODEL               指定模型名称 (默认: Mistral-7B-Instruct-v0.3)"
            echo "  --base_url URL              指定vLLM服务器地址"
            echo "  --port PORT                 指定vLLM服务器端口"
            echo "  --num_samples N             评估样本数量 (默认: 50)"
            echo "  --few_shot N                Few-shot示例数量 (默认: 3)"
            echo "  --consistency N             Self-consistency采样次数 (默认: 1)"
            echo "  --quick                     快速测试模式 (20道题)"
            echo "  --full                      完整评估模式 (100道题)"
            echo "  --output_dir DIR            结果输出目录"
            echo "  -h, --help                  显示此帮助信息"
            echo ""
            echo "功能说明:"
            echo "  本脚本将对每道GSM8K题目依次使用四种prompt角色:"
            echo "  1. 直觉主义者 (intuitionist) - 零样本CoT，直觉推理"
            echo "  2. 逻辑学家 (logician) - 形式化推理，结构化方法"
            echo "  3. 怀疑论者 (skeptic) - 批判性思考，错误检测"
            echo "  4. 规划者 (planner) - 策略规划，分步解决"
            echo ""
            echo "  评估结果将包含:"
            echo "  - 每种角色的准确率对比"
            echo "  - 每道题上各角色的对错分布"
            echo "  - 角色间一致性分析"
            echo "  - 可视化图表"
            echo ""
            echo "示例:"
            echo "  $0 --quick                          # 快速测试20道题"
            echo "  $0 --full --port 8080               # 完整测试100道题，端口8080"
            echo "  $0 --num_samples 30 --few_shot 5    # 自定义30道题，5-shot"
            exit 0
            ;;
        *)
            echo "未知参数: $1" >&2
            echo "使用 --help 查看帮助信息" >&2
            exit 1
            ;;
    esac
done
echo "📋 GSM8K多角色对比评估配置："
echo "- 模型: $MODEL"
echo "- API地址: $BASE_URL"
echo "- 评估样本数: $NUM_SAMPLES"
echo "- Few-shot示例数: $FEW_SHOT"
echo "- Self-consistency采样次数: $N_CONSISTENCY_SAMPLES"
echo "- Temperature: $TEMPERATURE"
echo "- 输出目录: $OUTPUT_DIR"
echo "- 评估角色: 直觉主义者、逻辑学家、怀疑论者、规划者"
echo ""

echo "🔍 测试vLLM服务器连接 ($BASE_URL)..."

# 简单的连接测试
if curl -s "$BASE_URL/v1/models" >/dev/null 2>&1; then
    echo "✅ vLLM连接测试通过"
    
    # 运行多角色对比评估
    echo "🚀 开始多角色对比评估..."
    python src/role_comparison_evaluator.py \
        --model "$MODEL" \
        --base_url "$BASE_URL" \
        --num_samples $NUM_SAMPLES \
        --few_shot $FEW_SHOT \
        --n_consistency_samples $N_CONSISTENCY_SAMPLES \
        --temperature $TEMPERATURE \
        --top_p $TOP_P \
        --max_tokens $MAX_TOKENS \
        --confidence_temp $CONFIDENCE_TEMP \
        --output_dir "$OUTPUT_DIR" \
        2>&1 | tee logs/role_comparison/gsm8k_role_comparison_$(date +%Y%m%d_%H%M%S).log
        
    echo "✅ 多角色对比评估完成！"
    echo ""
    echo "📁 结果文件保存在: $OUTPUT_DIR"
    echo "📝 日志文件保存在: logs/role_comparison/"
    echo "📊 图表文件已生成，请查看输出目录中的PNG文件"
    echo ""
    echo "💡 提示："
    echo "   - 查看 role_comparison_final_*.json 了解详细结果"
    echo "   - 查看 *.png 文件了解可视化分析"
    echo "   - 对比不同角色在相同题目上的表现差异"
    
else
    echo "❌ vLLM连接失败 ($BASE_URL)"
    echo "   请确保vLLM服务器正在运行"
    echo "   启动命令示例:"
    echo "   python -m vllm.entrypoints.openai.api_server \\"
    echo "       --model $MODEL \\"
    echo "       --host 0.0.0.0 \\"
    echo "       --port $(echo $BASE_URL | grep -o '[0-9]*$') \\"
    echo "       --served-model-name $MODEL"
    exit 1
fi
