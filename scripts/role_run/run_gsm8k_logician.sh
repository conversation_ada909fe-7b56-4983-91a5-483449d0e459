#!/bin/bash

# GSM8K Dataset Evaluation Script
# 针对GSM8K数学推理任务的专用评估脚本
# 
# 使用方法:
#   ./scripts/run_gsm8k.sh [vllm|local] [--quick] [--few-shot N] [--consistency N]

echo "🧮 开始GSM8K数学推理评估..."

# 创建结    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [vllm|local] [选项]"
    echo ""
    echo "参数说明:"
    echo "  vllm|local         评估模式（推荐使用vLLM）"
    echo "  --quick            快速测试模式（10个样本，3次采样）"
    echo "  --few-shot N       设置few-shot示例数量（推荐3-8）"
    echo "  --zero-shot        零样本评估"
    echo "  --consistency N    设置self-consistency采样次数（推荐5-10）"
    echo "  --template NAME    指定prompt模板"
    echo "  --intuitionist     使用直觉主义者模板（零样本CoT）"
    echo "  --logician         使用逻辑学家模板（形式化推理）"
    echo "  --skeptic          使用怀疑论者模板（批判性思考）"
    echo "  --planner          使用规划师模板（策略规划）"
    echo ""
    echo "Prompt模板说明："
    echo "  intuitionist - 直觉主义者：零样本CoT，\"让我们一步一步地思考\""
    echo "  logician     - 逻辑学家：形式化、结构化的方法，符号和方程推演"
    echo "  skeptic      - 怀疑论者：识别和分析潜在错误路径，批判性思考"
    echo "  planner      - 规划师：高层级策略规划，确保推理结构性和方向性"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                              # 默认直觉主义者模板"
    echo "  $0 vllm --logician                   # 使用逻辑学家模板"
    echo "  $0 vllm --skeptic --few-shot 5       # 怀疑论者模板，5-shot"
    echo "  $0 vllm --planner --consistency 10   # 规划师模板，10次采样"
    echo "  $0 vllm --template intuitionist --quick  # 快速测试直觉主义者"ults
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# GSM8K专用配置
# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
MODELS["Mistral-7B-Instruct-v0.3"]="2222"
# MODELS["gemma-2-2b"]="8079"
# MODELS["/data/qwen2.5-3b"]="8890"
# MODELS["Meta-Llama-3.1-8B-Instruct"]="6667"
#  MODELS["gemma-7b"]="7777"

DATASETS="gsm8k"
TEMPERATURE=0.7  # GSM8K使用采样生成，提高多样性
TOP_P=0.9
N_CONSISTENCY_SAMPLES=1  # GSM8K推荐使用self-consistency
MAX_TOKENS=512  # GSM8K需要生成完整的推理过程
FEW_SHOT=8  # GSM8K默认使用3-shot CoT
TEMPERATURE_CONFIDENCE=0.2  # 置信度分数softmax温度
USE_COT=True
USE_PATH_CONFIDENCE=False
COMBINED_CONFIDENCE_THRESHOLD=0.25
PROMPT_TEMPLATE="logician"  

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}
QUICK_TEST=false
CUSTOM_FEW_SHOT=""
CUSTOM_CONSISTENCY=""
CUSTOM_PROMPT_TEMPLATE=""

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --few-shot)
            CUSTOM_FEW_SHOT="$2"
            shift 2
            ;;
        --zero-shot)
            CUSTOM_FEW_SHOT="0"
            shift
            ;;
        --consistency)
            CUSTOM_CONSISTENCY="$2"
            shift 2
            ;;
        --template)
            CUSTOM_PROMPT_TEMPLATE="$2"
            shift 2
            ;;
        --intuitionist)
            CUSTOM_PROMPT_TEMPLATE="intuitionist"
            shift
            ;;
        --logician)
            CUSTOM_PROMPT_TEMPLATE="logician"
            shift
            ;;
        --skeptic)
            CUSTOM_PROMPT_TEMPLATE="skeptic"
            shift
            ;;
        --planner)
            CUSTOM_PROMPT_TEMPLATE="planner"
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot] [--consistency N] [--template TEMPLATE]"
            exit 1
            ;;
    esac
done

# 设置few-shot数量
if [ -n "$CUSTOM_FEW_SHOT" ]; then
    FEW_SHOT=$CUSTOM_FEW_SHOT
fi

# 设置consistency采样次数
if [ -n "$CUSTOM_CONSISTENCY" ]; then
    N_CONSISTENCY_SAMPLES=$CUSTOM_CONSISTENCY
fi

# 设置prompt模板
if [ -n "$CUSTOM_PROMPT_TEMPLATE" ]; then
    PROMPT_TEMPLATE=$CUSTOM_PROMPT_TEMPLATE
fi

# 快速测试模式配置
if [ "$QUICK_TEST" == true ]; then
    NUM_SAMPLES=10
    N_CONSISTENCY_SAMPLES=3  # 快速测试减少采样次数
    echo "🏃 快速测试模式：使用 $NUM_SAMPLES 个样本，$N_CONSISTENCY_SAMPLES 次采样"
else
    NUM_SAMPLES=1319
    echo "📊 完整评估模式：使用 $NUM_SAMPLES 个样本，$N_CONSISTENCY_SAMPLES 次采样"
fi

echo "📋 GSM8K评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 模型配置:"
for model_path in "${!MODELS[@]}"; do
    port=${MODELS[$model_path]}
    model_name=$(basename "$model_path")
    echo "  * $model_name (端口: $port)"
done
echo "- 数据集: $DATASETS"
echo "- Prompt模板: $PROMPT_TEMPLATE"
echo "- Few-shot示例数: $FEW_SHOT (Chain-of-Thought)"
echo "- Temperature: $TEMPERATURE (采样生成)"
echo "- Max tokens: $MAX_TOKENS (完整推理)"
echo "- Self-consistency采样次数: $N_CONSISTENCY_SAMPLES"
echo "- 评估样本数: $NUM_SAMPLES"
echo ""

if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🚀 使用vLLM API评估GSM8K..."
    
    # 遍历所有配置的模型
    for model_path in "${!MODELS[@]}"; do
        port=${MODELS[$model_path]}
        model_name=$(basename "$model_path")
        base_url="http://localhost:$port"
        
        echo ""
        echo "🤖 评估模型: $model_name"
        echo "   - 模型路径: $model_path"
        echo "   - API地址: $base_url"
        
        # 测试当前模型的vLLM连接
        echo "🔍 测试vLLM服务器连接 ($base_url)..."
        
        # 简单的连接测试
        if curl -s "$base_url/v1/models" >/dev/null 2>&1; then
            echo "✅ vLLM连接测试通过"
            
            # 创建模型专用日志文件夹
            mkdir -p "logs/$model_name"
            
            # 运行GSM8K评估
            echo "开始GSM8K评估..."
            python run_vllm_baseline.py \
                --models "$model_path" \
                --base_url "$base_url" \
                --datasets $DATASETS \
                --few_shot $FEW_SHOT \
                --temperature $TEMPERATURE \
                --top_p $TOP_P \
                --confidence_temp $TEMPERATURE_CONFIDENCE \
                --n_consistency_samples $N_CONSISTENCY_SAMPLES \
                --use_cot $USE_COT \
                --use_path_confidence $USE_PATH_CONFIDENCE \
                --combined_confidence_threshold $COMBINED_CONFIDENCE_THRESHOLD \
                --prompt_template $PROMPT_TEMPLATE \
                --max_tokens $MAX_TOKENS \
                --num_samples $NUM_SAMPLES \
                2>&1 | tee logs/$model_name/gsm8k_${model_name}_evaluation_${PROMPT_TEMPLATE}_$(date +%Y%m%d_%H%M%S).log
                
            echo "✅ 模型 $model_name 的GSM8K评估完成！"
        else
            echo "❌ vLLM连接失败 ($base_url)，跳过模型 $model_name"
            echo "   请确保vLLM服务器在端口 $port 上运行"
            continue
        fi
    done
    
elif [ "$EVALUATION_MODE" == "local" ]; then
    echo "🏠 使用本地模型评估GSM8K..."
    echo "⚠️ 本地模式暂未针对GSM8K优化，建议使用vLLM模式"
    
    # 创建本地模式日志文件夹
    mkdir -p "logs/local"
    
    python run_local_baseline.py \
        2>&1 | tee logs/local/gsm8k_local_evaluation_$(date +%Y%m%d_%H%M%S).log

else
    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot] [--consistency N]"
    echo ""
    echo "参数说明:"
    echo "  vllm|local      评估模式（推荐使用vLLM）"
    echo "  --quick         快速测试模式（10个样本，3次采样）"
    echo "  --few-shot N    设置few-shot示例数量（推荐3-5）"
    echo "  --zero-shot     零样本评估"
    echo "  --consistency N 设置self-consistency采样次数（推荐5-10）"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                        # 默认3-shot CoT，5次采样"
    echo "  $0 vllm --quick                # 快速测试"
    echo "  $0 vllm --few-shot 5           # 5-shot CoT"
    echo "  $0 vllm --consistency 10       # 10次self-consistency采样"
    echo "  $0 vllm --zero-shot --consistency 1  # 零样本，单次采样"
    echo ""
    echo "💡 建议：GSM8K推荐使用few-shot CoT + self-consistency获得最佳效果"
    exit 1
fi

echo ""
echo "📁 结果保存在 results/ 目录下"
echo "📝 日志保存在 logs/ 目录下"
echo ""
echo "💡 提示：可以查看日志了解详细的推理过程和答案提取情况"
