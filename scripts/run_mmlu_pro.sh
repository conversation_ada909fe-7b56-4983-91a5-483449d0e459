#!/bin/bash

# MMLU-Pro Dataset Evaluation Script
# 针对MMLU-Pro高难度多选择题任务的专用评估脚本
# 
# 使用方法:
#   ./scripts/run_mmlu_pro.sh [vllm|local] [--quick] [--few-shot N]

echo "🎓 开始MMLU-Pro高难度多选择题评估..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# MMLU-Pro专用配置
# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
# MODELS["/data/Mistral-7B-Instruct-v0.3"]="8888"
# MODELS["/data/gemma-2-2b"]="8889"
# MODELS["/data/qwen2.5-3b"]="8890"
# MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8891"
MODELS["Meta-Llama-3.1-8B-Instruct"]="8789"

DATASETS="mmlu-pro"
TEMPERATURE=0.0  # MMLU-Pro使用贪婪解码
TOP_P=1.0
N_CONSISTENCY_SAMPLES=1  # MMLU-Pro单次采样
MAX_TOKENS=1  # MMLU-Pro只需要单个token（A/B/C/D/E/F/G/H/I/J）
FEW_SHOT=5  # MMLU-Pro默认使用5-shot
TEMPERATURE_CONFIDENCE=0.5  # 置信度分数softmax温度

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}
QUICK_TEST=false
CUSTOM_FEW_SHOT=""

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --few-shot)
            CUSTOM_FEW_SHOT="$2"
            shift 2
            ;;
        --zero-shot)
            CUSTOM_FEW_SHOT="0"
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot]"
            exit 1
            ;;
    esac
done

# 设置few-shot数量
if [ -n "$CUSTOM_FEW_SHOT" ]; then
    FEW_SHOT=$CUSTOM_FEW_SHOT
fi

# 快速测试模式配置
if [ "$QUICK_TEST" == true ]; then
    NUM_SAMPLES=10
    echo "🏃 快速测试模式：使用 $NUM_SAMPLES 个样本"
else
    NUM_SAMPLES=50
    echo "📊 完整评估模式：使用 $NUM_SAMPLES 个样本"
fi

echo "📋 MMLU-Pro评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 模型配置:"
for model_path in "${!MODELS[@]}"; do
    port=${MODELS[$model_path]}
    model_name=$(basename "$model_path")
    echo "  * $model_name (端口: $port)"
done
echo "- 数据集: $DATASETS"
echo "- Few-shot示例数: $FEW_SHOT"
echo "- Temperature: $TEMPERATURE (贪婪解码)"
echo "- Max tokens: $MAX_TOKENS (单token输出)"
echo "- Temperature (置信度): $TEMPERATURE_CONFIDENCE"
echo "- Consistency采样次数: $N_CONSISTENCY_SAMPLES"
echo "- 评估样本数: $NUM_SAMPLES"
echo ""

if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🚀 使用vLLM API评估MMLU-Pro..."
    
    # 遍历所有配置的模型
    for model_path in "${!MODELS[@]}"; do
        port=${MODELS[$model_path]}
        model_name=$(basename "$model_path")
        base_url="http://localhost:$port"
        
        echo ""
        echo "🤖 评估模型: $model_name"
        echo "   - 模型路径: $model_path"
        echo "   - API地址: $base_url"
        
        # 测试当前模型的vLLM连接
        echo "🔍 测试vLLM服务器连接 ($base_url)..."
        
        # 简单的连接测试
        if curl -s "$base_url/v1/models" >/dev/null 2>&1; then
            echo "✅ vLLM连接测试通过"
            
            # 运行MMLU-Pro评估
            echo "开始MMLU-Pro评估..."
            python run_vllm_baseline.py \
                --models "$model_path" \
                --base_url "$base_url" \
                --datasets $DATASETS \
                --few_shot $FEW_SHOT \
                --temperature $TEMPERATURE \
                --top_p $TOP_P \
                --confidence_temp $TEMPERATURE_CONFIDENCE \
                --n_consistency_samples $N_CONSISTENCY_SAMPLES \
                --max_tokens $MAX_TOKENS \
                --num_samples $NUM_SAMPLES \
                2>&1 | tee logs/mmlu_pro_${model_name}_evaluation_$(date +%Y%m%d_%H%M%S).log
                
            echo "✅ 模型 $model_name 的MMLU-Pro评估完成！"
        else
            echo "❌ vLLM连接失败 ($base_url)，跳过模型 $model_name"
            echo "   请确保vLLM服务器在端口 $port 上运行"
            continue
        fi
    done
    
elif [ "$EVALUATION_MODE" == "local" ]; then
    echo "🏠 使用本地模型评估MMLU-Pro..."
    echo "⚠️ 本地模式暂未针对MMLU-Pro优化，建议使用vLLM模式"
    
    python run_local_baseline.py \
        2>&1 | tee logs/mmlu_pro_local_evaluation_$(date +%Y%m%d_%H%M%S).log

else
    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot]"
    echo ""
    echo "参数说明:"
    echo "  vllm|local      评估模式（推荐使用vLLM）"
    echo "  --quick         快速测试模式（10个样本）"
    echo "  --few-shot N    设置few-shot示例数量"
    echo "  --zero-shot     零样本评估"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                    # 默认5-shot，50样本"
    echo "  $0 vllm --quick            # 默认5-shot，10样本"
    echo "  $0 vllm --zero-shot        # 零样本，50样本"
    echo "  $0 vllm --few-shot 3       # 3-shot，50样本"
    echo ""
    echo "⚠️ 注意：MMLU-Pro难度较高，建议使用few-shot模式"
    exit 1
fi

echo ""
echo "📁 结果保存在 results/ 目录下"
echo "📝 日志保存在 logs/ 目录下"
