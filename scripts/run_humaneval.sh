#!/bin/bash

# HumanEval Dataset Evaluation Script
# 针对HumanEval代码生成任务的专用评估脚本
# 支持Chain-of-Thought (CoT) 和 Self-Consistency (SC)
# 
# 使用方法:
#   ./scripts/run_humaneval.sh [vllm|local] [--quick] [--few-shot N] [--consistency N] [--dataset humaneval]

echo "💻 开始HumanEval代码生成评估..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 加载HumanEval配置
HUMANEVAL_CONFIG="configs/humaneval_config.json"
FEW_SHOT_EXAMPLES="configs/humaneval_few_shot_examples.json"

# HumanEval专用配置
# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
# MODELS["/data/Mistral-7B-Instruct-v0.3"]="8888"
# MODELS["gemma-2-2b"]="8079"
# MODELS["/data/qwen2.5-3b"]="8890"

### gpu 5
# MODELS["Meta-Llama-3.1-8B-Instruct"]="8789"

### gpu 3
# MODELS["Meta-Llama-3.1-8B-Instruct"]="8895"

### gpu 6&7
# MODELS["Meta-Llama-3.1-8B-Instruct"]="8869"
MODELS["gemma-7b"]="7777"

# 数据集选择 - 支持多个HumanEval变体
DATASETS="humaneval"  # 默认使用标准HumanEval
TEMPERATURE=0.2  # HumanEval代码生成使用较低温度保证准确性
TOP_P=0.95
N_CONSISTENCY_SAMPLES=10  # HumanEval推荐使用moderate self-consistency
MAX_TOKENS=1024  # HumanEval需要生成完整的代码函数
FEW_SHOT=0  # HumanEval通常使用zero-shot，但支持few-shot
TEMPERATURE_CONFIDENCE=0.1  # 置信度分数softmax温度，代码任务用更低温度
USE_COT=True
USE_PATH_CONFIDENCE=True
COMBINED_CONFIDENCE_THRESHOLD=0.3  # 代码任务使用稍高的阈值

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}
QUICK_TEST=false
CUSTOM_FEW_SHOT=""
CUSTOM_CONSISTENCY=""
CUSTOM_DATASET=""

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --few-shot)
            CUSTOM_FEW_SHOT="$2"
            shift 2
            ;;
        --zero-shot)
            CUSTOM_FEW_SHOT="0"
            shift
            ;;
        --consistency)
            CUSTOM_CONSISTENCY="$2"
            shift 2
            ;;
        --dataset)
            CUSTOM_DATASET="$2"
            shift 2
            ;;
        --cot)
            USE_COT=true
            shift
            ;;
        --no-cot)
            USE_COT=false
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot] [--consistency N] [--dataset DATASET] [--cot|--no-cot]"
            exit 1
            ;;
    esac
done

# 设置数据集
if [ -n "$CUSTOM_DATASET" ]; then
    DATASETS=$CUSTOM_DATASET
fi

# 设置few-shot数量
if [ -n "$CUSTOM_FEW_SHOT" ]; then
    FEW_SHOT=$CUSTOM_FEW_SHOT
fi

# 设置consistency采样次数
if [ -n "$CUSTOM_CONSISTENCY" ]; then
    N_CONSISTENCY_SAMPLES=$CUSTOM_CONSISTENCY
fi

# 根据数据集调整配置
case $DATASETS in
    "humaneval")
        NUM_SAMPLES_FULL=164  # HumanEval标准测试集大小
        TASK_TYPE="代码生成"
        ;;
    "humaneval-x")
        NUM_SAMPLES_FULL=164  # HumanEval-X每种语言的测试集大小
        TASK_TYPE="多语言代码生成"
        ;;
    "mbpp")
        NUM_SAMPLES_FULL=500  # MBPP测试集大小
        TASK_TYPE="Python代码生成"
        ;;
    *)
        NUM_SAMPLES_FULL=164  # 默认使用HumanEval大小
        TASK_TYPE="代码生成"
        ;;
esac

# 快速测试模式配置
if [ "$QUICK_TEST" == true ]; then
    NUM_SAMPLES=10
    N_CONSISTENCY_SAMPLES=3  # 快速测试减少采样次数
    echo "🏃 快速测试模式：使用 $NUM_SAMPLES 个样本，$N_CONSISTENCY_SAMPLES 次采样"
else
    NUM_SAMPLES=$NUM_SAMPLES_FULL
    echo "📊 完整评估模式：使用 $NUM_SAMPLES 个样本，$N_CONSISTENCY_SAMPLES 次采样"
fi

echo "📋 HumanEval评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 模型配置:"
for model_path in "${!MODELS[@]}"; do
    port=${MODELS[$model_path]}
    model_name=$(basename "$model_path")
    echo "  * $model_name (端口: $port)"
done
echo "- 数据集: $DATASETS ($TASK_TYPE)"
echo "- Few-shot示例数: $FEW_SHOT"
if [ "$USE_COT" == true ]; then
    echo "- Chain-of-Thought: 启用 (逐步推理)"
else
    echo "- Chain-of-Thought: 禁用 (直接生成)"
fi
echo "- Temperature: $TEMPERATURE (代码生成精确模式)"
echo "- Max tokens: $MAX_TOKENS (完整函数生成)"
echo "- Self-consistency采样次数: $N_CONSISTENCY_SAMPLES"
echo "- 评估样本数: $NUM_SAMPLES"
echo ""

if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🚀 使用vLLM API评估HumanEval..."
    
    # 遍历所有配置的模型
    for model_path in "${!MODELS[@]}"; do
        port=${MODELS[$model_path]}
        model_name=$(basename "$model_path")
        base_url="http://localhost:$port"
        
        echo ""
        echo "🤖 评估模型: $model_name"
        echo "   - 模型路径: $model_path"
        echo "   - API地址: $base_url"
        
        # 测试当前模型的vLLM连接
        echo "🔍 测试vLLM服务器连接 ($base_url)..."
        
        # 简单的连接测试
        if curl -s "$base_url/v1/models" >/dev/null 2>&1; then
            echo "✅ vLLM连接测试通过"
            
            # 运行HumanEval评估
            echo "开始 $DATASETS 评估..."
            python run_vllm_baseline.py \
                --models "$model_path" \
                --base_urls "$base_url" \
                --datasets $DATASETS \
                --few_shot $FEW_SHOT \
                --temperature $TEMPERATURE \
                --top_p $TOP_P \
                --confidence_temp $TEMPERATURE_CONFIDENCE \
                --n_consistency_samples $N_CONSISTENCY_SAMPLES \
                --use_cot $USE_COT \
                --use_path_confidence $USE_PATH_CONFIDENCE \
                --combined_confidence_threshold $COMBINED_CONFIDENCE_THRESHOLD \
                --max_tokens $MAX_TOKENS \
                --num_samples $NUM_SAMPLES \
                2>&1 | tee logs/${DATASETS}_${model_name}_evaluation_$(date +%Y%m%d_%H%M%S).log
                
            echo "✅ 模型 $model_name 的 $DATASETS 评估完成！"
            
            # 如果是HumanEval，还可以运行代码执行测试
            if [ "$DATASETS" == "humaneval" ]; then
                echo "🔧 开始代码执行测试..."
                
                # 查找最新的结果文件
                LATEST_RESULT=$(ls -t results/${DATASETS}_${model_name}_*.json 2>/dev/null | head -1)
                
                if [ -n "$LATEST_RESULT" ] && [ -f "$LATEST_RESULT" ]; then
                    echo "📝 对结果文件进行代码执行评估: $LATEST_RESULT"
                    
                    # 运行代码执行评估
                    python evaluate_humaneval_execution.py \
                        --results_file "$LATEST_RESULT" \
                        --output_file "${LATEST_RESULT%.json}_execution_eval.json" \
                        --timeout 10 \
                        2>&1 | tee logs/${DATASETS}_${model_name}_execution_eval_$(date +%Y%m%d_%H%M%S).log
                        
                    echo "✅ 代码执行评估完成！"
                else
                    echo "⚠️ 未找到结果文件，跳过代码执行评估"
                fi
            fi
            
        else
            echo "❌ vLLM连接失败 ($base_url)，跳过模型 $model_name"
            echo "   请确保vLLM服务器在端口 $port 上运行"
            continue
        fi
    done
    
elif [ "$EVALUATION_MODE" == "local" ]; then
    echo "🏠 使用本地模型评估HumanEval..."
    echo "⚠️ 本地模式暂未针对HumanEval优化，建议使用vLLM模式"
    
    python run_local_baseline.py \
        --datasets $DATASETS \
        --few_shot $FEW_SHOT \
        --temperature $TEMPERATURE \
        --use_cot $USE_COT \
        --max_tokens $MAX_TOKENS \
        --num_samples $NUM_SAMPLES \
        2>&1 | tee logs/${DATASETS}_local_evaluation_$(date +%Y%m%d_%H%M%S).log

else
    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [vllm|local] [选项...]"
    echo ""
    echo "参数说明:"
    echo "  vllm|local           评估模式（推荐使用vLLM）"
    echo "  --quick              快速测试模式（10个样本，3次采样）"
    echo "  --few-shot N         设置few-shot示例数量（0-5）"
    echo "  --zero-shot          零样本评估（默认）"
    echo "  --consistency N      设置self-consistency采样次数（推荐5-15）"
    echo "  --dataset DATASET    选择数据集（humaneval, humaneval-x, mbpp）"
    echo "  --cot                启用Chain-of-Thought推理（默认）"
    echo "  --no-cot             禁用Chain-of-Thought推理"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                                    # 默认zero-shot，10次采样"
    echo "  $0 vllm --quick                            # 快速测试"
    echo "  $0 vllm --few-shot 3 --cot                 # 3-shot CoT"
    echo "  $0 vllm --consistency 15                   # 15次self-consistency采样"
    echo "  $0 vllm --dataset humaneval-x              # HumanEval-X多语言版本"
    echo "  $0 vllm --dataset mbpp --few-shot 5        # MBPP数据集，5-shot"
    echo "  $0 vllm --no-cot --consistency 1           # 直接生成，无CoT"
    echo ""
    echo "💡 建议："
    echo "   - HumanEval: zero-shot + moderate self-consistency (5-10次)"
    echo "   - 复杂任务: few-shot CoT + higher self-consistency (10-15次)"
    echo "   - 快速测试: --quick + --consistency 3"
    exit 1
fi

echo ""
echo "📁 结果保存在 results/ 目录下"
echo "📝 日志保存在 logs/ 目录下"
echo ""
echo "💡 提示："
echo "   - 查看日志了解详细的代码生成过程"
echo "   - HumanEval结果需要通过代码执行测试验证正确性"
echo "   - 可以使用 evaluate_code_execution.py 进行后续验证"
