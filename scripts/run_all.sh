#!/bin/bash

# Comprehensive Evaluation Script
# 批量运行所有数据集的评估脚本
# 
# 使用方法:
#   ./scripts/run_all.sh [vllm|local] [--quick]

echo "🚀 开始综合评估 - 所有数据集..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 切换到项目目录
cd "$PROJECT_DIR"

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}
QUICK_TEST=false

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick]"
            exit 1
            ;;
    esac
done

# 设置运行参数
if [ "$QUICK_TEST" == true ]; then
    QUICK_FLAG="--quick"
    echo "🏃 快速测试模式"
else
    QUICK_FLAG=""
    echo "📊 完整评估模式"
fi

echo "📋 评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 测试模式: $([ "$QUICK_TEST" == true ] && echo "快速测试" || echo "完整评估")"
echo ""

# 检查vLLM连接（如果使用vLLM模式）
if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🔍 测试vLLM服务器连接..."
    python test_vllm_connection.py
    
    if [ $? -ne 0 ]; then
        echo "❌ vLLM连接失败，请检查服务器状态"
        exit 1
    fi
    echo "✅ vLLM连接测试通过"
    echo ""
fi

# 创建综合评估日志
COMPREHENSIVE_LOG="logs/comprehensive_evaluation_$(date +%Y%m%d_%H%M%S).log"
echo "📝 综合评估日志: $COMPREHENSIVE_LOG"
echo ""

# 开始评估
echo "开始综合评估..." | tee "$COMPREHENSIVE_LOG"
echo "评估开始时间: $(date)" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"

# 1. MMLU评估
echo "==================== 1. MMLU 评估 ====================" | tee -a "$COMPREHENSIVE_LOG"
echo "🧠 开始MMLU评估..." | tee -a "$COMPREHENSIVE_LOG"
bash "$SCRIPT_DIR/run_mmlu.sh" $EVALUATION_MODE $QUICK_FLAG 2>&1 | tee -a "$COMPREHENSIVE_LOG"
MMLU_EXIT_CODE=$?
echo "MMLU评估完成，退出码: $MMLU_EXIT_CODE" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"

# 2. MMLU-Pro评估
echo "==================== 2. MMLU-Pro 评估 ====================" | tee -a "$COMPREHENSIVE_LOG"
echo "🎓 开始MMLU-Pro评估..." | tee -a "$COMPREHENSIVE_LOG"
bash "$SCRIPT_DIR/run_mmlu_pro.sh" $EVALUATION_MODE $QUICK_FLAG 2>&1 | tee -a "$COMPREHENSIVE_LOG"
MMLU_PRO_EXIT_CODE=$?
echo "MMLU-Pro评估完成，退出码: $MMLU_PRO_EXIT_CODE" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"

# 3. GSM8K评估
echo "==================== 3. GSM8K 评估 ====================" | tee -a "$COMPREHENSIVE_LOG"
echo "🧮 开始GSM8K评估..." | tee -a "$COMPREHENSIVE_LOG"
bash "$SCRIPT_DIR/run_gsm8k.sh" $EVALUATION_MODE $QUICK_FLAG 2>&1 | tee -a "$COMPREHENSIVE_LOG"
GSM8K_EXIT_CODE=$?
echo "GSM8K评估完成，退出码: $GSM8K_EXIT_CODE" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"

# 评估总结
echo "==================== 评估总结 ====================" | tee -a "$COMPREHENSIVE_LOG"
echo "评估完成时间: $(date)" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"
echo "各数据集评估结果:" | tee -a "$COMPREHENSIVE_LOG"
echo "- MMLU:     $([ $MMLU_EXIT_CODE -eq 0 ] && echo "✅ 成功" || echo "❌ 失败 (退出码: $MMLU_EXIT_CODE)")" | tee -a "$COMPREHENSIVE_LOG"
echo "- MMLU-Pro: $([ $MMLU_PRO_EXIT_CODE -eq 0 ] && echo "✅ 成功" || echo "❌ 失败 (退出码: $MMLU_PRO_EXIT_CODE)")" | tee -a "$COMPREHENSIVE_LOG"
echo "- GSM8K:    $([ $GSM8K_EXIT_CODE -eq 0 ] && echo "✅ 成功" || echo "❌ 失败 (退出码: $GSM8K_EXIT_CODE)")" | tee -a "$COMPREHENSIVE_LOG"
echo "" | tee -a "$COMPREHENSIVE_LOG"

# 计算总体成功率
TOTAL_TASKS=3
SUCCESSFUL_TASKS=0
[ $MMLU_EXIT_CODE -eq 0 ] && SUCCESSFUL_TASKS=$((SUCCESSFUL_TASKS + 1))
[ $MMLU_PRO_EXIT_CODE -eq 0 ] && SUCCESSFUL_TASKS=$((SUCCESSFUL_TASKS + 1))
[ $GSM8K_EXIT_CODE -eq 0 ] && SUCCESSFUL_TASKS=$((SUCCESSFUL_TASKS + 1))

echo "总体评估结果: $SUCCESSFUL_TASKS/$TOTAL_TASKS 个任务成功完成" | tee -a "$COMPREHENSIVE_LOG"

if [ $SUCCESSFUL_TASKS -eq $TOTAL_TASKS ]; then
    echo "🎉 所有评估任务都成功完成！" | tee -a "$COMPREHENSIVE_LOG"
    FINAL_EXIT_CODE=0
else
    echo "⚠️ 部分评估任务失败，请检查日志" | tee -a "$COMPREHENSIVE_LOG"
    FINAL_EXIT_CODE=1
fi

echo "" | tee -a "$COMPREHENSIVE_LOG"
echo "📁 所有结果保存在 results/ 目录下" | tee -a "$COMPREHENSIVE_LOG"
echo "📝 详细日志保存在 logs/ 目录下" | tee -a "$COMPREHENSIVE_LOG"
echo "📊 综合评估日志: $COMPREHENSIVE_LOG" | tee -a "$COMPREHENSIVE_LOG"

exit $FINAL_EXIT_CODE
