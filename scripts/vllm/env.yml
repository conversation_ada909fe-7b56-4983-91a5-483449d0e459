name: vllm
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.7.15=h06a4308_0
  - expat=2.7.1=h6a678d5_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxcb=1.17.0=h9b100fa_0
  - ncurses=6.5=h7934f7d_0
  - openssl=3.0.17=h5eee18b_0
  - pip=25.1=pyhc872135_2
  - pthread-stubs=0.3=h0ce48e5_1
  - python=3.11.13=h1a3bd86_0
  - readline=8.3=hc2a1206_0
  - setuptools=78.1.1=py311h06a4308_0
  - sqlite=3.50.2=hb25bd0a_1
  - tk=8.6.14=h993c535_1
  - tzdata=2025b=h04d1e81_0
  - wheel=0.45.1=py311h06a4308_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.12.15
      - aiosignal==1.4.0
      - annotated-types==0.7.0
      - anyio==4.10.0
      - astor==0.8.1
      - attrs==25.3.0
      - blake3==1.0.5
      - cachetools==6.1.0
      - cbor2==5.6.5
      - certifi==2025.8.3
      - cffi==1.17.1
      - charset-normalizer==3.4.3
      - click==8.2.1
      - cloudpickle==3.1.1
      - compressed-tensors==0.10.2
      - cupy-cuda12x==13.5.1
      - depyf==0.19.0
      - dill==0.4.0
      - diskcache==5.6.3
      - distro==1.9.0
      - dnspython==2.7.0
      - einops==0.8.1
      - email-validator==2.2.0
      - fastapi==0.116.1
      - fastapi-cli==0.0.8
      - fastapi-cloud-cli==0.1.5
      - fastrlock==0.8.3
      - filelock==3.18.0
      - frozenlist==1.7.0
      - fsspec==2025.7.0
      - gguf==0.17.1
      - h11==0.16.0
      - hf-xet==1.1.7
      - httpcore==1.0.9
      - httptools==0.6.4
      - httpx==0.28.1
      - huggingface-hub==0.34.4
      - idna==3.10
      - interegular==0.3.3
      - jinja2==3.1.6
      - jiter==0.10.0
      - jsonschema==4.25.0
      - jsonschema-specifications==2025.4.1
      - lark==1.2.2
      - llguidance==0.7.30
      - llvmlite==0.44.0
      - lm-format-enforcer==0.10.12
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - mdurl==0.1.2
      - mistral-common==1.8.3
      - mpmath==1.3.0
      - msgpack==1.1.1
      - msgspec==0.19.0
      - multidict==6.6.3
      - networkx==3.5
      - ninja==1.11.1.4
      - numba==0.61.2
      - numpy==2.2.6
      - nvidia-cublas-cu12==12.6.4.1
      - nvidia-cuda-cupti-cu12==12.6.80
      - nvidia-cuda-nvrtc-cu12==12.6.77
      - nvidia-cuda-runtime-cu12==12.6.77
      - nvidia-cudnn-cu12==9.5.1.17
      - nvidia-cufft-cu12==11.3.0.4
      - nvidia-cufile-cu12==1.11.1.6
      - nvidia-curand-cu12==10.3.7.77
      - nvidia-cusolver-cu12==11.7.1.2
      - nvidia-cusparse-cu12==12.5.4.2
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-nccl-cu12==2.26.2
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.6.77
      - openai==1.90.0
      - opencv-python-headless==4.12.0.88
      - outlines-core==0.2.10
      - packaging==25.0
      - partial-json-parser==*******.post6
      - pillow==11.3.0
      - prometheus-client==0.22.1
      - prometheus-fastapi-instrumentator==7.1.0
      - propcache==0.3.2
      - protobuf==6.31.1
      - psutil==7.0.0
      - py-cpuinfo==9.0.0
      - pybase64==1.4.2
      - pycountry==24.6.1
      - pycparser==2.22
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pydantic-extra-types==2.10.5
      - pygments==2.19.2
      - python-dotenv==1.1.1
      - python-json-logger==3.3.0
      - python-multipart==0.0.20
      - pyyaml==6.0.2
      - pyzmq==27.0.1
      - ray==2.48.0
      - referencing==0.36.2
      - regex==2025.7.34
      - requests==2.32.4
      - rich==14.1.0
      - rich-toolkit==0.14.9
      - rignore==0.6.4
      - rpds-py==0.27.0
      - safetensors==0.6.2
      - scipy==1.16.1
      - sentencepiece==0.2.0
      - sentry-sdk==2.34.1
      - shellingham==1.5.4
      - sniffio==1.3.1
      - soundfile==0.13.1
      - soxr==0.5.0.post1
      - starlette==0.47.2
      - sympy==1.14.0
      - tiktoken==0.11.0
      - tokenizers==0.21.4
      - torch==2.7.1
      - torchaudio==2.7.1
      - torchvision==0.22.1
      - tqdm==4.67.1
      - transformers==4.55.0
      - triton==3.3.1
      - typer==0.16.0
      - typing-extensions==4.14.1
      - typing-inspection==0.4.1
      - urllib3==2.5.0
      - uvicorn==0.35.0
      - uvloop==0.21.0
      - vllm==0.10.0
      - watchfiles==1.1.0
      - websockets==15.0.1
      - xformers==0.0.31
      - xgrammar==0.1.21
      - yarl==1.20.1
prefix: /data/depo/conda_env/vllm
