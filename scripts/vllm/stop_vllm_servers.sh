#!/bin/bash

# 停止vLLM服务器脚本

echo "🛑 停止vLLM服务器..."

# 查找并停止所有vLLM服务器进程
stopped_count=0

# 方法1: 通过PID文件停止
if [ -d "logs" ]; then
    for pid_file in logs/vllm_*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            model_name=$(basename "$pid_file" .pid)
            
            echo "停止 $model_name (PID: $pid)..."
            
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                sleep 2
                
                # 如果进程还在运行，强制杀死
                if kill -0 "$pid" 2>/dev/null; then
                    kill -9 "$pid"
                    echo "⚠️ 强制停止 $model_name"
                else
                    echo "✅ 已停止 $model_name"
                fi
                
                stopped_count=$((stopped_count + 1))
            else
                echo "⚠️ 进程 $pid 不存在"
            fi
            
            # 删除PID文件
            rm "$pid_file"
        fi
    done
fi

# 方法2: 通过进程名停止（备用方法）
echo "🔍 查找剩余的vLLM进程..."
vllm_pids=$(pgrep -f "vllm.entrypoints.openai.api_server")

if [ -n "$vllm_pids" ]; then
    echo "发现剩余vLLM进程: $vllm_pids"
    for pid in $vllm_pids; do
        echo "停止进程 $pid..."
        kill "$pid"
        sleep 1
        
        # 检查是否还在运行
        if kill -0 "$pid" 2>/dev/null; then
            kill -9 "$pid"
            echo "⚠️ 强制停止进程 $pid"
        fi
        stopped_count=$((stopped_count + 1))
    done
fi

# 方法3: 检查并停止占用相关端口的进程
echo "🔍 检查端口占用..."
ports=(8000 8001 8002 8889 8882)

for port in "${ports[@]}"; do
    pid=$(lsof -ti:$port)
    if [ -n "$pid" ]; then
        echo "停止占用端口 $port 的进程: $pid"
        kill "$pid" 2>/dev/null
        sleep 1
        
        # 强制停止如果还在运行
        if kill -0 "$pid" 2>/dev/null; then
            kill -9 "$pid" 2>/dev/null
        fi
        stopped_count=$((stopped_count + 1))
    fi
done

if [ $stopped_count -gt 0 ]; then
    echo "✅ 已停止 $stopped_count 个进程"
else
    echo "ℹ️ 没有发现运行中的vLLM服务器"
fi

# 清理日志文件（可选）
read -p "是否删除vLLM日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f logs/vllm_*.log
    echo "✅ 已清理日志文件"
else
    echo "ℹ️ 保留日志文件"
fi

echo "🏁 vLLM服务器停止完成"
