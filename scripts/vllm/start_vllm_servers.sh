#!/bin/bash
# vLLM 服务器启动脚本 (支持指定GPU)

echo "🚀 开始启动 vLLM 服务器 (支持指定GPU)..."

# --- 配置区 ---
# 格式: MODELS["/path/to/model"]="端口号:GPU编号"
#
# 单GPU示例: "8000:0"                  (在 GPU 0 上运行)
# 多GPU示例: "9000:0,1"                (使用 GPU 0 和 1 并行运行一个模型)
# --------------------------------------------------------------------------
declare -A MODELS

MODELS["/data/Mistral-7B-Instruct-v0.3"]="2222:0"

# MODELS["/data/Mistral-7B-Instruct-v0.3"]="3333:5"



# MODELS["/data/gemma-2-2b"]="8079:4"
# MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8781:2"
# MODELS["/data/Qwen2.5-3B-Instruct"]="8890:2"
# MODELS["/data/qwen2.5-3b"]="8990:2"

# 如果你想用多个GPU运行一个大模型，可以这样配置：
# MODELS["/data/Llama-3-70B"]="9000:0,1,2,3"

# 启动参数 (可以直接修改)
GPU_MEMORY_UTILIZATION="0.9"  # GPU 内存使用率 (多GPU时建议设高一点)
MAX_MODEL_LEN="4096"          # 最大序列长度
# --- 配置结束 ---

# 确保日志目录存在
mkdir -p logs

# 遍历并启动所有模型
for model_path in "${!MODELS[@]}"; do
    config=${MODELS[$model_path]}
    model_name=$(basename "$model_path")

    # 1. 解析端口和GPU ID
    port=$(echo "$config" | cut -d':' -f1)
    gpu_ids=$(echo "$config" | cut -d':' -f2)

    # 检查配置格式是否正确
    if [ -z "$port" ] || [ -z "$gpu_ids" ]; then
        echo "❌ 错误: 模型 '$model_name' 的配置格式不正确 ('$config')。应为 '端口:GPU编号'，跳过。"
        continue
    fi

    # 检查模型目录是否存在
    if [ ! -d "$model_path" ]; then
        echo "❌ 错误: 找不到模型目录 '$model_path'，跳过。"
        continue
    fi

    # 2. 计算需要使用的GPU数量，用于 --tensor-parallel-size
    #   通过计算逗号数量再加1来得出GPU总数
    num_gpus=$(echo "$gpu_ids" | tr -cd ',' | wc -c)
    tensor_parallel_size=$((num_gpus + 1))

    echo "--------------------------------------------------"
    echo "🚀 正在后台启动模型: $model_name"
    echo "   - 端口: $port"
    echo "   - 指定GPU: $gpu_ids"
    echo "   - 并行规模 (TP size): $tensor_parallel_size"
    echo "   - 日志: logs/${model_name}.log"

    # 3. 使用 CUDA_VISIBLE_DEVICES 环境变量和 --tensor-parallel-size 参数启动服务
    CUDA_VISIBLE_DEVICES="$gpu_ids" nohup /data/depo/conda_env/vllm/bin/python -m vllm.entrypoints.openai.api_server \
        --model "$model_path" \
        --port "$port" \
        --host "0.0.0.0" \
        --tensor-parallel-size "$tensor_parallel_size" \
        --gpu-memory-utilization "$GPU_MEMORY_UTILIZATION" \
        --max-model-len "$MAX_MODEL_LEN" \
        --trust-remote-code \
        --served-model-name $(basename "$model_path") \
        > "logs/${model_name}.log" 2>&1 &

done

echo "--------------------------------------------------"
echo "✅ 所有服务器均已尝试启动。"
echo ""
echo "🔍 如何管理服务器:"
echo "   - 查看GPU使用情况: watch nvidia-smi"
echo "   - 查看所有vLLM进程: ps -ef | grep vllm"
echo "   - 查看某个模型的日志: tail -f logs/模型名.log"
echo "   - 停止所有vLLM服务: pkill -f vllm.entrypoints.openai.api_server"
echo ""
echo "🔗 API 端点 (启动后几分钟可用):"
for model_path in "${!MODELS[@]}"; do
    if [ -d "$model_path" ]; then
        config=${MODELS[$model_path]}
        port=$(echo "$config" | cut -d':' -f1)
        model_name=$(basename "$model_path")
        echo "   - $model_name: http://localhost:$port/v1/models"
    fi
done