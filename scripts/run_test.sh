#!/bin/bash

# System Test Script
# 系统功能测试脚本
# 
# 使用方法:
#   ./scripts/run_test.sh [basic|full]

echo "🧪 开始系统测试..."

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 切换到项目目录
cd "$PROJECT_DIR"

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 测试模式选择
TEST_MODE=${1:-"basic"}

echo "📋 测试配置："
echo "- 测试模式: $TEST_MODE"
echo "- 项目目录: $PROJECT_DIR"
echo ""

# 基础系统测试
echo "==================== 基础系统测试 ===================="

# 1. Python环境测试
echo "🐍 测试Python环境..."
python --version
if [ $? -eq 0 ]; then
    echo "✅ Python环境正常"
else
    echo "❌ Python环境异常"
    exit 1
fi

# 2. 依赖包测试
echo ""
echo "📦 测试依赖包..."
python -c "import torch, transformers, datasets; print('✅ 核心依赖包导入成功')" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 核心依赖包正常"
else
    echo "❌ 核心依赖包缺失，请检查requirements.txt"
fi

# 3. 数据集文件测试
echo ""
echo "📁 测试数据集文件..."
DATASETS_FOUND=0
if [ -f "dataset/mmlu/mmlu_test.csv" ]; then
    echo "✅ MMLU数据集存在"
    DATASETS_FOUND=$((DATASETS_FOUND + 1))
else
    echo "⚠️ MMLU数据集缺失"
fi

if [ -f "dataset/mmlu-pro/mmlu_pro_test.csv" ]; then
    echo "✅ MMLU-Pro数据集存在"
    DATASETS_FOUND=$((DATASETS_FOUND + 1))
else
    echo "⚠️ MMLU-Pro数据集缺失"
fi

if [ -f "dataset/gsm8k/gsm8k_test.json" ]; then
    echo "✅ GSM8K数据集存在"
    DATASETS_FOUND=$((DATASETS_FOUND + 1))
else
    echo "⚠️ GSM8K数据集缺失"
fi

echo "📊 数据集检测结果: $DATASETS_FOUND/3 个数据集可用"

# 4. vLLM连接测试
echo ""
echo "🔗 测试vLLM服务器连接..."
python test_vllm_connection.py
VLLM_STATUS=$?

if [ $VLLM_STATUS -eq 0 ]; then
    echo "✅ vLLM服务器连接正常"
    VLLM_AVAILABLE=true
else
    echo "⚠️ vLLM服务器不可用"
    VLLM_AVAILABLE=false
fi

# 基础测试总结
echo ""
echo "==================== 基础测试总结 ===================="
echo "- Python环境: ✅"
echo "- 依赖包: $(python -c "import torch, transformers, datasets" 2>/dev/null && echo "✅" || echo "❌")"
echo "- 数据集: $DATASETS_FOUND/3 可用"
echo "- vLLM服务器: $([ "$VLLM_AVAILABLE" == true ] && echo "✅" || echo "⚠️")"

# 如果是完整测试模式
if [ "$TEST_MODE" == "full" ]; then
    echo ""
    echo "==================== 完整功能测试 ===================="
    
    # 5. 数据集加载测试
    echo "📊 测试数据集加载功能..."
    if [ $DATASETS_FOUND -gt 0 ]; then
        python -c "
from src.local_datasets import get_local_dataset_processor
datasets = ['mmlu', 'mmlu-pro', 'gsm8k']
for dataset in datasets:
    try:
        processor = get_local_dataset_processor(dataset)
        print(f'✅ {dataset.upper()} 数据集加载成功: {len(processor.dataset) if processor.dataset else 0} 个样本')
    except Exception as e:
        print(f'❌ {dataset.upper()} 数据集加载失败: {e}')
"
    else
        echo "⚠️ 跳过数据集加载测试（无可用数据集）"
    fi
    
    # 6. Few-shot功能测试
    echo ""
    echo "🎯 测试Few-shot功能..."
    python -c "
from src.local_datasets import get_local_dataset_processor
try:
    # 测试GSM8K的few-shot功能
    processor = get_local_dataset_processor('gsm8k')
    examples = processor.get_few_shot_examples(2)
    print(f'✅ GSM8K Few-shot功能正常: 获取到 {len(examples)} 个示例')
except Exception as e:
    print(f'❌ Few-shot功能测试失败: {e}')
" 2>/dev/null || echo "⚠️ Few-shot功能测试跳过"

    # 7. 小规模评估测试
    if [ "$VLLM_AVAILABLE" == true ] && [ $DATASETS_FOUND -gt 0 ]; then
        echo ""
        echo "🚀 运行小规模评估测试..."
        echo "测试GSM8K评估（1个样本）..."
        
        python run_vllm_baseline.py \
            --datasets gsm8k \
            --few_shot 2 \
            --num_samples 1 \
            --n_consistency_samples 2 \
            --temperature 0.7 \
            --max_tokens 128 \
            > /tmp/test_evaluation.log 2>&1
            
        if [ $? -eq 0 ]; then
            echo "✅ 小规模评估测试通过"
        else
            echo "❌ 小规模评估测试失败，查看 /tmp/test_evaluation.log"
        fi
    else
        echo "⚠️ 跳过评估测试（vLLM不可用或无数据集）"
    fi
    
    # 完整测试总结
    echo ""
    echo "==================== 完整测试总结 ===================="
    BASIC_SCORE=0
    ADVANCED_SCORE=0
    
    # 基础功能评分
    [ -x "$(command -v python)" ] && BASIC_SCORE=$((BASIC_SCORE + 1))
    python -c "import torch, transformers, datasets" 2>/dev/null && BASIC_SCORE=$((BASIC_SCORE + 1))
    [ $DATASETS_FOUND -gt 0 ] && BASIC_SCORE=$((BASIC_SCORE + 1))
    [ "$VLLM_AVAILABLE" == true ] && BASIC_SCORE=$((BASIC_SCORE + 1))
    
    echo "基础功能评分: $BASIC_SCORE/4"
    
    if [ $BASIC_SCORE -eq 4 ]; then
        echo "🎉 系统基础功能完全正常！"
        echo "💡 建议：可以开始正式的模型评估"
    elif [ $BASIC_SCORE -ge 2 ]; then
        echo "⚠️ 系统基本可用，但存在一些问题"
        echo "💡 建议：解决缺失的组件后再进行评估"
    else
        echo "❌ 系统存在严重问题，无法正常使用"
        echo "💡 建议：检查环境配置和依赖安装"
    fi
fi

echo ""
echo "📁 如有问题，请检查相关日志文件"
echo "📝 更多信息请参考 README.md 和 USAGE_GUIDE.md"

# 输出建议的下一步操作
echo ""
echo "💡 建议的下一步操作："
if [ "$VLLM_AVAILABLE" == true ] && [ $DATASETS_FOUND -gt 0 ]; then
    echo "1. 运行快速评估: ./scripts/run_gsm8k.sh vllm --quick"
    echo "2. 运行完整评估: ./scripts/run_all.sh vllm"
    echo "3. 运行超参数搜索: ./scripts/run_hyperparameter_search.sh gsm8k --quick"
elif [ $DATASETS_FOUND -gt 0 ]; then
    echo "1. 启动vLLM服务器: bash start_vllm_servers.sh"
    echo "2. 重新运行测试: ./scripts/run_test.sh"
else
    echo "1. 下载数据集: python download_datasets.py"
    echo "2. 启动vLLM服务器: bash start_vllm_servers.sh"
    echo "3. 重新运行测试: ./scripts/run_test.sh"
fi
