#!/bin/bash

# Hyperparameter Search Script
# 针对不同数据集的超参数搜索脚本
# 
# 使用方法:
#   ./scripts/run_hyperparameter_search.sh [dataset] [--trials N] [--samples N] [--quick]
#   参数说明:
#     dataset: 数据集名称 (mmlu, mmlu-pro, gsm8k)
#     --trials N: 搜索试验次数 (默认40)
#     --samples N: 每次试验的评估样本数 (默认20)
#     --quick: 快速测试模式 (5次试验, 10个样本)

echo "🔍 开始超参数搜索..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
# MODELS["/data/Mistral-7B-Instruct-v0.3"]="8888"
# MODELS["/data/gemma-2-2b"]="8889"
# MODELS["/data/qwen2.5-3b"]="8890"
# MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8891"
MODELS["/data/Qwen2.5-3B-Instruct"]="8890"

# 默认配置
DATASET=${1:-"mmlu"} # mmlu, MMLU-Pro, GSM8K
N_TRIALS=40
N_EVAL_SAMPLES=20  # 每次试验的评估样本数
QUICK_TEST=false

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --trials)
            N_TRIALS="$2"
            shift 2
            ;;
        --samples)
            N_EVAL_SAMPLES="$2"
            shift 2
            ;;
        --quick)
            QUICK_TEST=true
            N_TRIALS=5
            N_EVAL_SAMPLES=10  # 快速测试时减少评估样本
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [dataset] [--trials N] [--samples N] [--quick]"
            exit 1
            ;;
    esac
done

# 根据数据集选择配置文件
case $DATASET in
    "mmlu")
        CONFIG_FILE="mmlu_search_config.json"
        STUDY_NAME="mmlu_hyperparameter_search"
        echo "🧠 MMLU超参数搜索"
        ;;
    "mmlu-pro")
        CONFIG_FILE="mmlu_pro_search_config.json"
        STUDY_NAME="mmlu_pro_hyperparameter_search"
        echo "🎓 MMLU-Pro超参数搜索"
        ;;
    "gsm8k")
        CONFIG_FILE="gsm8k_config.json"
        STUDY_NAME="gsm8k_hyperparameter_search"
        echo "🧮 GSM8K超参数搜索"
        ;;
    *)
        echo "❌ 不支持的数据集: $DATASET"
        echo "支持的数据集: mmlu, mmlu-pro, gsm8k"
        exit 1
        ;;
esac

# 获取第一个配置的模型用于超参数搜索
SELECTED_MODEL=""
SELECTED_PORT=""
for model_path in "${!MODELS[@]}"; do
    SELECTED_MODEL="$model_path"
    SELECTED_PORT="${MODELS[$model_path]}"
    break  # 只使用第一个模型进行超参数搜索
done

if [ -z "$SELECTED_MODEL" ]; then
    echo "❌ 未配置任何模型，请检查MODELS配置"
    exit 1
fi

SELECTED_BASE_URL="http://localhost:$SELECTED_PORT"
SELECTED_MODEL_NAME=$(basename "$SELECTED_MODEL")

echo "📋 选择的模型配置："
echo "- 模型: $SELECTED_MODEL_NAME"
echo "- 端口: $SELECTED_PORT"
echo "- API地址: $SELECTED_BASE_URL"
echo ""

# 创建配置文件（如果不存在）
if [ ! -f "$CONFIG_FILE" ]; then
    echo "📝 创建配置文件: $CONFIG_FILE"
    
    case $DATASET in
        "mmlu")
            cat > "$CONFIG_FILE" << EOF
{
    "search_config": {
        "dataset": "mmlu",
        "split": "test",
        "temperature": {
            "type": "float",
            "low": 0.0,
            "high": 0.5
        },
        "few_shot": {
            "type": "int",
            "low": 0,
            "high": 10
        }
    },
    "evaluation_config": {
        "model_name": "$SELECTED_MODEL_NAME",
        "base_url": "$SELECTED_BASE_URL",
        "top_p": 1.0,
        "max_tokens": 1,
        "n_consistency_samples": 1,
        "n_eval_samples": $N_EVAL_SAMPLES
    },
    "output_config": {
        "results_dir": "results",
        "log_dir": "logs"
    }
}
EOF
            ;;
        "mmlu-pro")
            cat > "$CONFIG_FILE" << EOF
{
    "search_config": {
        "dataset": "mmlu-pro",
        "split": "test",
        "temperature": {
            "type": "float",
            "low": 0.0,
            "high": 0.5
        },
        "few_shot": {
            "type": "int",
            "low": 0,
            "high": 8
        }
    },
    "evaluation_config": {
        "model_name": "$SELECTED_MODEL_NAME",
        "base_url": "$SELECTED_BASE_URL",
        "top_p": 1.0,
        "max_tokens": 1,
        "n_consistency_samples": 1,
        "n_eval_samples": $N_EVAL_SAMPLES
    },
    "output_config": {
        "results_dir": "results",
        "log_dir": "logs"
    }
}
EOF
            ;;
        "gsm8k")
            cat > "$CONFIG_FILE" << EOF
{
    "search_config": {
        "dataset": "gsm8k",
        "split": "test",
        "n_consistency_samples": {
            "type": "int",
            "low": 3,
            "high": 10
        },
        "temperature": {
            "type": "float",
            "low": 0.1,
            "high": 1.0
        }
    },
    "evaluation_config": {
        "model_name": "$SELECTED_MODEL_NAME",
        "base_url": "$SELECTED_BASE_URL",
        "top_p": 0.9,
        "max_tokens": 512,
        "few_shot": 3,
        "n_eval_samples": $N_EVAL_SAMPLES
    },
    "output_config": {
        "results_dir": "results",
        "log_dir": "logs"
    }
}
EOF
            ;;
    esac
fi

echo "📋 超参数搜索配置："
echo "- 数据集: $DATASET"
echo "- 配置文件: $CONFIG_FILE"
echo "- 搜索次数: $N_TRIALS"
echo "- 评估样本数: $N_EVAL_SAMPLES"
echo "- 研究名称: $STUDY_NAME"
echo ""

# 测试vLLM连接
echo "🔍 测试vLLM服务器连接..."
if curl -s "$SELECTED_BASE_URL/v1/models" >/dev/null 2>&1; then
    echo "✅ vLLM连接测试通过 ($SELECTED_BASE_URL)"
else
    echo "❌ vLLM连接失败 ($SELECTED_BASE_URL)，请检查服务器状态"
    exit 1
fi

# 运行超参数搜索
echo "开始超参数搜索..."
python hyperparameter_search_v2.py \
    --config "$CONFIG_FILE" \
    --mode search \
    --n_trials $N_TRIALS \
    2>&1 | tee logs/hyperparameter_search_${DATASET}_$(date +%Y%m%d_%H%M%S).log
    
SEARCH_EXIT_CODE=$?

if [ $SEARCH_EXIT_CODE -eq 0 ]; then
    echo "✅ 超参数搜索完成！"
    
    # 显示最佳结果
    echo ""
    echo "🏆 查看搜索结果..."
    python hyperparameter_search_v2.py \
        --config "$CONFIG_FILE" \
        --mode analyze
else
    echo "❌ 超参数搜索失败，请检查日志"
    exit $SEARCH_EXIT_CODE
fi

echo ""
echo "📁 结果保存在 results/ 目录下"
echo "📝 日志保存在 logs/ 目录下"
echo "🔍 数据库文件: ${STUDY_NAME}.db"
echo ""
echo "💡 提示："
echo "- 可以使用 --mode analyze 查看详细分析结果"
echo "- 可以使用 --mode visualize 生成可视化图表"
