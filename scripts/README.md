# Scripts 使用指南

这个目录包含了针对不同任务和数据集优化的评估脚本。每个脚本都根据特定任务的特点设置了最优的参数配置。

## 📁 脚本概览

### 单数据集评估脚本

| 脚本 | 任务类型 | 数据集 | 特点 |
|------|----------|--------|------|
| `run_mmlu.sh` | 多选择题 | MMLU | 贪婪解码，单token输出 |
| `run_mmlu_pro.sh` | 高难度多选择题 | MMLU-Pro | 贪婪解码，单token输出 |
| `run_gsm8k.sh` | 数学推理 | GSM8K | 采样生成，Self-Consistency |

### 综合脚本

| 脚本 | 功能 | 描述 |
|------|------|------|
| `run_all.sh` | 批量评估 | 运行所有数据集评估 |
| `run_hyperparameter_search.sh` | 超参数搜索 | 自动搜索最优参数 |
| `run_test.sh` | 系统测试 | 检查环境和功能 |

## 🚀 快速开始

### 1. 系统测试
```bash
# 基础测试
./scripts/run_test.sh

# 完整功能测试
./scripts/run_test.sh full
```

### 2. 单数据集评估
```bash
# MMLU评估（默认5-shot，100样本）
./scripts/run_mmlu.sh vllm

# GSM8K评估（默认3-shot CoT，50样本，5次self-consistency）
./scripts/run_gsm8k.sh vllm

# MMLU-Pro评估（默认5-shot，50样本）
./scripts/run_mmlu_pro.sh vllm
```

### 3. 快速测试模式
```bash
# 快速测试（少量样本）
./scripts/run_mmlu.sh vllm --quick
./scripts/run_gsm8k.sh vllm --quick
./scripts/run_mmlu_pro.sh vllm --quick
```

### 4. 自定义参数
```bash
# 自定义few-shot数量
./scripts/run_mmlu.sh vllm --few-shot 3
./scripts/run_gsm8k.sh vllm --few-shot 5

# 零样本评估
./scripts/run_mmlu.sh vllm --zero-shot
./scripts/run_gsm8k.sh vllm --zero-shot

# GSM8K自定义consistency采样次数
./scripts/run_gsm8k.sh vllm --consistency 10
```

### 5. 批量评估
```bash
# 运行所有数据集
./scripts/run_all.sh vllm

# 快速批量测试
./scripts/run_all.sh vllm --quick
```

### 6. 超参数搜索
```bash
# GSM8K超参数搜索
./scripts/run_hyperparameter_search.sh gsm8k

# 快速搜索（5次试验）
./scripts/run_hyperparameter_search.sh gsm8k --quick

# 自定义试验次数
./scripts/run_hyperparameter_search.sh gsm8k --trials 50

# MMLU超参数搜索
./scripts/run_hyperparameter_search.sh mmlu --trials 30
```

## ⚙️ 脚本详细说明

### MMLU脚本 (`run_mmlu.sh`)
**任务特点**：多选择题，需要精确的选项选择
**优化配置**：
- `temperature=0.0`：贪婪解码保证一致性
- `max_tokens=1`：只需要单个选项字母
- `n_consistency_samples=1`：单次采样即可
- `few_shot=5`：默认5-shot获得更好效果

**使用示例**：
```bash
./scripts/run_mmlu.sh vllm                    # 默认配置
./scripts/run_mmlu.sh vllm --quick            # 快速测试
./scripts/run_mmlu.sh vllm --zero-shot        # 零样本
./scripts/run_mmlu.sh vllm --few-shot 10      # 10-shot
```

### GSM8K脚本 (`run_gsm8k.sh`)
**任务特点**：数学推理，需要完整的推理过程
**优化配置**：
- `temperature=0.7`：采样生成增加多样性
- `max_tokens=512`：足够生成完整推理
- `n_consistency_samples=5`：Self-Consistency提高准确性
- `few_shot=3`：3-shot CoT获得推理能力

**使用示例**：
```bash
./scripts/run_gsm8k.sh vllm                        # 默认配置
./scripts/run_gsm8k.sh vllm --quick                # 快速测试
./scripts/run_gsm8k.sh vllm --consistency 10       # 10次采样
./scripts/run_gsm8k.sh vllm --few-shot 5           # 5-shot CoT
```

### MMLU-Pro脚本 (`run_mmlu_pro.sh`)
**任务特点**：高难度多选择题，选项更多
**优化配置**：
- `temperature=0.0`：贪婪解码
- `max_tokens=1`：单token输出
- `n_consistency_samples=1`：单次采样
- `few_shot=5`：5-shot应对高难度

### 超参数搜索脚本 (`run_hyperparameter_search.sh`)
**功能**：自动搜索最优的超参数组合
**支持的参数**：
- GSM8K：`n_consistency_samples`, `temperature`
- MMLU：`temperature`, `few_shot`
- MMLU-Pro：`temperature`, `few_shot`

**使用示例**：
```bash
./scripts/run_hyperparameter_search.sh gsm8k --trials 20
./scripts/run_hyperparameter_search.sh mmlu --quick
```

## 📊 结果输出

所有脚本都会在以下位置保存结果：

- **评估结果**：`results/` 目录下的JSON文件
- **日志文件**：`logs/` 目录下的详细日志
- **超参数搜索**：SQLite数据库文件 + 最优参数报告

## 🔧 环境要求

1. **vLLM服务器**：运行在 `http://localhost:8889`
2. **数据集**：放在 `dataset/` 目录下
3. **Python环境**：安装所有依赖包

## 💡 最佳实践

1. **首次使用**：先运行 `./scripts/run_test.sh full` 检查环境
2. **快速验证**：使用 `--quick` 模式进行快速测试
3. **正式评估**：使用默认参数获得可靠结果
4. **超参数优化**：在确定基础功能正常后进行搜索
5. **批量评估**：使用 `run_all.sh` 进行综合评估

## 🚨 常见问题

1. **vLLM连接失败**：检查服务器是否启动，端口是否正确
2. **数据集缺失**：运行 `python download_datasets.py` 下载数据
3. **内存不足**：使用 `--quick` 模式或减少样本数量
4. **权限问题**：确保脚本有执行权限 `chmod +x scripts/*.sh`

## 📈 性能优化建议

1. **MMLU类任务**：使用贪婪解码 + few-shot
2. **GSM8K类任务**：使用采样 + Self-Consistency + CoT
3. **批量评估**：建议分时段运行，避免长时间占用GPU
4. **超参数搜索**：从少量试验开始，逐步增加
