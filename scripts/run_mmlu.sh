#!/bin/bash

# MMLU Dataset Evaluation Script
# 针对MMLU多选择题任务的专用评估脚本
# 
# 使用方法:
#   ./scripts/run_mmlu.sh [vllm|local] [--quick] [--few-shot N]

echo "🧠 开始MMLU多选择题评估..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# MMLU专用配置
# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
# MODELS["/data/Mistral-7B-Instruct-v0.3"]="8888"
# MODELS["/data/gemma-2-2b"]="8079"
MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8781"
# MODELS["/data/Qwen2.5-3B-Instruct"]="8890"
# MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8891"

DATASETS="mmlu"
TEMPERATURE=0.2  # MMLU使用贪婪解码更稳定
TOP_P=1.0
N_CONSISTENCY_SAMPLES=5  # MMLU单次采样即可
MAX_TOKENS=20  # MMLU只需要单个token（A/B/C/D）
FEW_SHOT=5  # MMLU默认使用5-shot
TEMPERATURE_CONFIDENCE=0.5  # 置信度分数softmax温度
USE_COT=False  # 默认禁用CoT

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}
QUICK_TEST=false
CUSTOM_FEW_SHOT=""

# 解析命令行参数
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --few-shot)
            CUSTOM_FEW_SHOT="$2"
            shift 2
            ;;
        --zero-shot)
            CUSTOM_FEW_SHOT="0"
            shift
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot]"
            exit 1
            ;;
    esac
done

# 设置few-shot数量
if [ -n "$CUSTOM_FEW_SHOT" ]; then
    FEW_SHOT=$CUSTOM_FEW_SHOT
fi

# 快速测试模式配置
if [ "$QUICK_TEST" == true ]; then
    NUM_SAMPLES=20
    echo "🏃 快速测试模式：使用 $NUM_SAMPLES 个样本"
else
    NUM_SAMPLES=5000
    echo "📊 完整评估模式：使用 $NUM_SAMPLES 个样本"
fi

echo "📋 MMLU评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 模型配置:"
for model_path in "${!MODELS[@]}"; do
    port=${MODELS[$model_path]}
    model_name=$(basename "$model_path")
    echo "  * $model_name (端口: $port)"
done
echo "- 数据集: $DATASETS"
echo "- Few-shot示例数: $FEW_SHOT"
echo "- Temperature: $TEMPERATURE (贪婪解码)"
echo "- Temperature (置信度): $TEMPERATURE_CONFIDENCE"
echo "- Max tokens: $MAX_TOKENS (单token输出)"
echo "- Consistency采样次数: $N_CONSISTENCY_SAMPLES"
echo "- 评估样本数: $NUM_SAMPLES"
echo ""

if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🚀 使用vLLM API评估MMLU..."
    
    # 遍历所有配置的模型
    for model_path in "${!MODELS[@]}"; do
        port=${MODELS[$model_path]}
        model_name=$(basename "$model_path")
        base_url="http://localhost:$port"
        
        echo ""
        echo "🤖 评估模型: $model_name"
        echo "   - 模型路径: $model_path"
        echo "   - API地址: $base_url"
        
        # 测试当前模型的vLLM连接
        echo "🔍 测试vLLM服务器连接 ($base_url)..."
        
        # 简单的连接测试
        if curl -s "$base_url/v1/models" >/dev/null 2>&1; then
            echo "✅ vLLM连接测试通过"
            
            # 运行MMLU评估
            echo "开始MMLU评估..."
            python run_vllm_baseline.py \
                --models "$model_name" \
                --base_urls "$base_url" \
                --datasets $DATASETS \
                --few_shot $FEW_SHOT \
                --temperature $TEMPERATURE \
                --top_p $TOP_P \
                --use_cot $USE_COT \
                --confidence_temp $TEMPERATURE_CONFIDENCE \
                --n_consistency_samples $N_CONSISTENCY_SAMPLES \
                --max_tokens $MAX_TOKENS \
                --num_samples $NUM_SAMPLES \
                2>&1 | tee logs/mmlu_${model_name}_evaluation_$(date +%Y%m%d_%H%M%S).log
                
            echo "✅ 模型 $model_name 的MMLU评估完成！"
        else
            echo "❌ vLLM连接失败 ($base_url)，跳过模型 $model_name"
            echo "   请确保vLLM服务器在端口 $port 上运行"
            continue
        fi
    done
    
elif [ "$EVALUATION_MODE" == "local" ]; then
    echo "🏠 使用本地模型评估MMLU..."
    echo "⚠️ 本地模式暂未针对MMLU优化，建议使用vLLM模式"
    
    python run_local_baseline.py \
        2>&1 | tee logs/mmlu_local_evaluation_$(date +%Y%m%d_%H%M%S).log

else
    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N] [--zero-shot]"
    echo ""
    echo "参数说明:"
    echo "  vllm|local      评估模式（推荐使用vLLM）"
    echo "  --quick         快速测试模式（20个样本）"
    echo "  --few-shot N    设置few-shot示例数量"
    echo "  --zero-shot     零样本评估"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                    # 默认5-shot，100样本"
    echo "  $0 vllm --quick            # 默认5-shot，20样本"
    echo "  $0 vllm --zero-shot        # 零样本，100样本"
    echo "  $0 vllm --few-shot 3       # 3-shot，100样本"
    exit 1
fi

echo ""
echo "📁 结果保存在 results/ 目录下"
echo "📝 日志保存在 logs/ 目录下"
