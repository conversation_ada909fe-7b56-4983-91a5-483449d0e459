# Scripts 文件夹修改总结

## 修改概览

已成功将 `scripts/` 文件夹内的所有评估脚本统一修改为支持多模型配置的格式，与 `start_vllm_servers.sh` 保持一致。

## 修改的文件

### ✅ 已修改的脚本

1. **`scripts/run_mmlu.sh`** - MMLU多选择题评估
2. **`scripts/run_gsm8k.sh`** - GSM8K数学推理评估  
3. **`scripts/run_mmlu_pro.sh`** - MMLU-Pro高难度多选择题评估

### ⏸️ 未修改的脚本 (无需修改)

1. **`scripts/run_all.sh`** - 综合评估脚本 (调用其他脚本)
2. **`scripts/run_hyperparameter_search.sh`** - 超参数搜索 (调用Python脚本)
3. **`scripts/run_test.sh`** - 系统测试脚本

## 统一的配置格式

所有修改后的脚本都采用以下统一格式：

```bash
# 模型配置：格式为 MODELS["/path/to/model"]="端口号"
# 确保端口号与vLLM服务器启动配置一致
declare -A MODELS
# MODELS["/data/Mistral-7B-Instruct-v0.3"]="8888"
# MODELS["/data/gemma-2-2b"]="8889"
# MODELS["/data/qwen2.5-3b"]="8890"
MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8891"
```

## 各脚本特定配置

### MMLU (`run_mmlu.sh`)
- **任务类型**: 多选择题 (A/B/C/D)
- **Temperature**: 0.0 (贪婪解码)
- **Max tokens**: 1 (单token输出)
- **Few-shot**: 5 (标准MMLU设置)
- **Consistency**: 1 (单次采样)

### GSM8K (`run_gsm8k.sh`)
- **任务类型**: 数学推理
- **Temperature**: 0.7 (采样生成)
- **Max tokens**: 512 (完整推理过程)
- **Few-shot**: 3 (Chain-of-Thought)
- **Consistency**: 5 (Self-consistency)

### MMLU-Pro (`run_mmlu_pro.sh`)
- **任务类型**: 高难度多选择题 (A-J)
- **Temperature**: 0.0 (贪婪解码)
- **Max tokens**: 1 (单token输出)
- **Few-shot**: 5 (标准设置)
- **Consistency**: 1 (单次采样)

## 新功能特性

### 1. 多模型支持
- 可同时配置多个模型
- 每个模型独立评估
- 失败模型不影响其他模型

### 2. 自动端口映射
- 模型路径 → 端口号 → API地址自动构建
- 与 `start_vllm_servers.sh` 端口配置对应

### 3. 独立日志管理
```bash
logs/mmlu_gemma-2-2b_evaluation_20250731_120000.log
logs/gsm8k_Meta-Llama-3.1-8B-Instruct_evaluation_20250731_120100.log
logs/mmlu_pro_qwen2.5-3b_evaluation_20250731_120200.log
```

### 4. 增强的配置显示
```
📋 MMLU评估配置：
- 评估模式: vllm
- 模型配置:
  * gemma-2-2b (端口: 8889)
  * Meta-Llama-3.1-8B-Instruct (端口: 8891)
- 数据集: mmlu
- Few-shot示例数: 5
...
```

### 5. 智能连接测试
- 每个模型独立连接测试
- 连接失败自动跳过
- 详细的错误信息显示

## 使用方法

### 启用单个模型 (默认设置)
保持当前配置不变，只有一个模型启用。

### 启用多个模型
取消注释想要的模型行：
```bash
declare -A MODELS
MODELS["/data/gemma-2-2b"]="8889"           # 启用
MODELS["/data/qwen2.5-3b"]="8890"           # 启用
MODELS["/data/Meta-Llama-3.1-8B-Instruct"]="8891"  # 启用
```

### 运行示例
```bash
# 单个数据集评估
./scripts/run_mmlu.sh vllm
./scripts/run_gsm8k.sh vllm --quick
./scripts/run_mmlu_pro.sh vllm --few-shot 3

# 综合评估 (所有数据集)
./scripts/run_all.sh vllm --quick
```

## 与 start_vllm_servers.sh 的对应关系

确保两个脚本中的配置一致：

**启动服务器 (`start_vllm_servers.sh`):**
```bash
MODELS["/data/gemma-2-2b"]="8889:7"  # 端口:GPU
```

**评估脚本 (各 `run_*.sh`):**
```bash
MODELS["/data/gemma-2-2b"]="8889"    # 仅端口
```

## 测试验证

所有修改的脚本都已通过语法检查：
- ✅ `scripts/run_mmlu.sh` - 语法正确
- ✅ `scripts/run_gsm8k.sh` - 语法正确  
- ✅ `scripts/run_mmlu_pro.sh` - 语法正确

## 总结

现在所有评估脚本都支持：
- 🔧 **统一配置格式** - 所有脚本使用相同的模型配置方式
- 🚀 **多模型支持** - 可同时评估多个模型
- 🔗 **自动端口映射** - 与vLLM服务器配置自动对应
- 📊 **独立结果管理** - 每个模型生成独立的日志和结果
- 🛡️ **容错处理** - 单个模型失败不影响整体评估

通过简单修改配置行，用户可以轻松切换或组合不同的模型进行评估！🎯
