#!/bin/bash

# Self-Consistency Baseline Evaluation Runner
# 用于批量运行不同配置的评估（支持本地模型和vLLM）
# 
# 使用方法:
#   ./run_baseline.sh [vllm|local] [--quick] [--few-shot N]
#
# 参数说明:
#   - 第一个参数: 评估模式 (vllm 或 local)
#   - --quick: 快速测试模式（少量样本）  
#   - --few-shot N: 设置few-shot示例数量

echo "开始Self-Consistency Baseline评估..."

# 创建结果目录
mkdir -p results
mkdir -p logs

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 基本配置
# MODELS="ministral-7b gemma-2-2b qwen2.5-3b"
MODELS="/data/gemma-2-2b"
# DATASETS="mmlu mmlu-pro gsm8k"
DATASETS="mmlu"
TEMPERATURE=0.7
TOP_P=0.9
NUM_SAMPLES=5
FEW_SHOT=3  # Few-shot示例数量，0表示zero-shot

# 评估模式选择
EVALUATION_MODE=${1:-"vllm"}  # local 或 vllm
QUICK_TEST=false
CUSTOM_FEW_SHOT=""

# 解析命令行参数
shift  # 跳过第一个参数（评估模式）
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --few-shot)
            CUSTOM_FEW_SHOT="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            echo "使用方法: $0 [vllm|local] [--quick] [--few-shot N]"
            exit 1
            ;;
    esac
done

# 设置few-shot数量
if [ -n "$CUSTOM_FEW_SHOT" ]; then
    FEW_SHOT=$CUSTOM_FEW_SHOT
fi

echo "评估配置："
echo "- 评估模式: $EVALUATION_MODE"
echo "- 模型: $MODELS"
echo "- 数据集: $DATASETS"
echo "- Few-shot示例数: $FEW_SHOT"
echo "- Temperature: $TEMPERATURE"
echo "- Top-p: $TOP_P"
echo "- Sampling次数: $NUM_SAMPLES"
echo ""

if [ "$EVALUATION_MODE" == "vllm" ]; then
    echo "🚀 使用vLLM API模式评估..."
    
    # 测试vLLM连接
    echo "🔍 测试vLLM服务器连接..."
    python test_vllm_connection.py
    
    if [ $? -eq 0 ]; then
        echo "✅ vLLM连接测试通过"
        
        # 运行vLLM评估
        echo "开始vLLM评估..."
        python run_vllm_baseline.py \
            --models $MODELS \
            --datasets $DATASETS \
            --few_shot $FEW_SHOT \
            --temperature $TEMPERATURE \
            --top_p $TOP_P \
            --n_consistency_samples $NUM_SAMPLES \
            --max_tokens 1 \
            2>&1 | tee logs/vllm_evaluation_$(date +%Y%m%d_%H%M%S).log
    else
        echo "❌ vLLM连接失败，请检查服务器状态"
        exit 1
    fi
    
elif [ "$EVALUATION_MODE" == "local" ]; then
    echo "🏠 使用本地模型模式评估..."
    
    # 检查本地模型
    echo "🔍 检查本地模型..."
    ls -la /data/ 2>/dev/null || echo "⚠️ /data/ 目录不存在"
    
    # 运行本地评估
    echo "开始本地模型评估..."
    python run_local_baseline.py \
        2>&1 | tee logs/local_evaluation_$(date +%Y%m%d_%H%M%S).log

else
    echo "❌ 无效的评估模式: $EVALUATION_MODE"
    echo "使用方法: $0 [local|vllm] [--quick] [--few-shot N]"
    echo ""
    echo "参数说明:"
    echo "  local|vllm    评估模式（本地模型或vLLM服务器）"
    echo "  --quick       快速测试模式（使用少量样本）"
    echo "  --few-shot N  设置few-shot示例数量（N为数字，0表示zero-shot）"
    echo ""
    echo "使用示例:"
    echo "  $0 vllm                    # 使用vLLM，zero-shot"
    echo "  $0 vllm --few-shot 3       # 使用vLLM，3-shot"
    echo "  $0 vllm --quick            # 使用vLLM，快速测试"
    echo "  $0 vllm --quick --few-shot 5  # 使用vLLM，快速测试，5-shot"
    exit 1
fi

echo "评估完成！"
echo "结果保存在 results/ 目录下"

# 可选：运行快速测试（少量样本）
if [ "$QUICK_TEST" == true ]; then
    echo ""
    echo "运行快速测试（50个样本）..."
    
    if [ "$EVALUATION_MODE" == "vllm" ]; then
        python run_vllm_baseline.py \
            --models $MODELS \
            --datasets $DATASETS \
            --few_shot $FEW_SHOT \
            --quick_test \
            --temperature $TEMPERATURE \
            --top_p $TOP_P \
            --n_consistency_samples $NUM_SAMPLES \
            2>&1 | tee logs/vllm_quick_test_$(date +%Y%m%d_%H%M%S).log
    else
        echo "快速测试模式目前仅支持vLLM评估"
    fi
fi
