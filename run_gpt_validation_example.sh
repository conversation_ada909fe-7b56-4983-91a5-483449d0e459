#!/bin/bash

# GPT验证置信度分数的使用示例
# 需要设置OPENAI_API_KEY环境变量或通过--gpt_api_key参数提供

# 基础使用示例（不启用GPT验证）
echo "运行基础评估（不启用GPT验证）..."
python profile_baseline_gpt_evaluate.py \
    --models "Meta-Llama-3.1-8B-Instruct" \
    --datasets "GSM8K" \
    --num_samples 10 \
    --n_consistency_samples 3 \
    --quick_test

echo "等待5秒..."
sleep 5

# 启用GPT验证的示例（需要设置OPENAI_API_KEY）
echo "运行带GPT验证的评估..."
export OPENAI_API_KEY="your-openai-api-key-here"  # 请替换为真实的API密钥

python profile_baseline_gpt_evaluate.py \
    --models "Meta-Llama-3.1-8B-Instruct" \
    --datasets "GSM8K" \
    --num_samples 10 \
    --n_consistency_samples 3 \
    --use_gpt_validation \
    --gpt_model "gpt-3.5-turbo" \
    --gpt_validation_sample_rate 0.3 \
    --quick_test

echo "评估完成！检查results目录查看GPT验证结果。"
